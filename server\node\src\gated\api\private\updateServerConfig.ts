// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { UpdateServerConfigRequest, ClientOS } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

export = async (req: RequestAs<UpdateServerConfigRequest>, res: ResponseAs<{}>) => {
  const { 
    os, 
    version, 
    gameServerAddress, 
    gameServerPort, 
    patchServerAddress, 
    guestModeAllowed, 
    customValue, 
    description 
  }: UpdateServerConfigRequest = req.body;

  mlog.info('[RX] /updateServerConfig', { body: req.body });

  try {
    // 입력 검증
    if (!os || !version) {
      throw new MError('Missing required parameters: os, version', MErrorCode.GATE_INVALID_REQUEST);
    }

    // OS 값 검증
    if (!Object.values(ClientOS).includes(os as ClientOS)) {
      throw new MError('Invalid OS value', MErrorCode.GATE_INVALID_OS);
    }

    // 버전 형식 검증
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(version)) {
      throw new MError('Invalid version format', MErrorCode.GATE_INVALID_VERSION);
    }

    // 게임 서버 포트 검증
    if (gameServerPort !== undefined && (gameServerPort < 1 || gameServerPort > 65535)) {
      throw new MError('Invalid game server port (must be 1-65535)', MErrorCode.GATE_INVALID_PORT);
    }

    // 게임 서버 주소 검증
    if (gameServerAddress !== undefined && gameServerAddress.length > 255) {
      throw new MError('Game server address too long (max 255 characters)', MErrorCode.GATE_ADDRESS_TOO_LONG);
    }

    // 패치 서버 주소 검증
    if (patchServerAddress !== undefined && patchServerAddress.length > 255) {
      throw new MError('Patch server address too long (max 255 characters)', MErrorCode.GATE_ADDRESS_TOO_LONG);
    }

    // 커스텀 값 길이 검증
    if (customValue !== undefined && customValue.length > 2000) {
      throw new MError('Custom value too long (max 2000 characters)', MErrorCode.GATE_CUSTOM_VALUE_TOO_LONG);
    }

    // 설명 길이 검증
    if (description !== undefined && description.length > 500) {
      throw new MError('Description too long (max 500 characters)', MErrorCode.GATE_DESCRIPTION_TOO_LONG);
    }

    await serverInfoService.updateServerConfig({ 
      os: os as ClientOS, 
      version,
      gameServerAddress,
      gameServerPort,
      patchServerAddress,
      guestModeAllowed,
      customValue,
      description
    });

    mlog.info('[TX] /updateServerConfig', { os, version });

    res.json({});
  } catch (error) {
    mlog.error('Error in updateServerConfig', error);
    
    if (error instanceof MError) {
      throw error;
    }
    
    throw new MError(error.message, MErrorCode.GATE_UPDATE_SERVER_CONFIG_ERROR, undefined, error.stack);
  }
};
