// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { GetServerInfoRequest, GetServerInfoResponse, ClientOS } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

export = async (req: RequestAs<GetServerInfoRequest>, res: ResponseAs<GetServerInfoResponse>) => {
  const { os, version }: GetServerInfoRequest = req.body;

  mlog.info('[RX] /getServerInfo', { body: req.body });

  try {
    // 입력 검증
    if (!os || !version) {
      throw new MError('Missing required parameters: os, version', MErrorCode.GATE_INVALID_REQUEST);
    }

    // OS 값 검증
    if (!Object.values(ClientOS).includes(os as ClientOS)) {
      throw new MError('Invalid OS value', MErrorCode.GATE_INVALID_OS);
    }

    // 버전 형식 검증 (예: 1.0.0)
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(version)) {
      throw new MError('Invalid version format', MErrorCode.GATE_INVALID_VERSION);
    }

    const serverInfo = await serverInfoService.getServerInfo({ os: os as ClientOS, version });

    mlog.info('[TX] /getServerInfo', { 
      os, 
      version, 
      state: serverInfo.state,
      gameServerAddress: serverInfo.gameServerAddress 
    });

    res.json(serverInfo);
  } catch (error) {
    mlog.error('Error in getServerInfo', error);
    
    if (error instanceof MError) {
      throw error;
    }
    
    throw new MError(error.message, MErrorCode.GATE_GET_SERVER_INFO_ERROR, undefined, error.stack);
  }
};
