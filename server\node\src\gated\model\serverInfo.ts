// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

export enum ServerState {
  ONLINE = 'online',
  OFFLINE = 'offline',
  RECOMMENDED_UPDATE = 'recommended_update',
  MAINTENANCE = 'maintenance',
  ACCESS_DENIED = 'access_denied',
  PATCH_ALLOWED = 'patch_allowed'
}

export enum ClientOS {
  WINDOWS = 'windows',
  ANDROID = 'android',
  IOS = 'ios'
}

export interface ServerConfig {
  id: number;
  os: ClientOS;
  version: string;
  state: ServerState;
  gameServerAddress: string;
  gameServerPort: number;
  patchServerAddress: string;
  guestModeAllowed: boolean;
  maintenanceMessage?: string;
  externalUrl?: string;
  customValue?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GetServerInfoRequest {
  os: ClientOS;
  version: string;
}

export interface GetServerInfoResponse {
  state: ServerState;
  gameServerAddress: string;
  gameServerPort: number;
  patchServerAddress: string;
  guestModeAllowed: boolean;
  maintenanceMessage?: string;
  externalUrl?: string;
  customValue?: string;
}

export interface UpdateServerStateRequest {
  os: ClientOS;
  version: string;
  state: ServerState;
}

export interface SetMaintenanceMessageRequest {
  os: ClientOS;
  version: string;
  maintenanceMessage: string;
  externalUrl?: string;
}

export interface UpdateServerConfigRequest {
  os: ClientOS;
  version: string;
  gameServerAddress?: string;
  gameServerPort?: number;
  patchServerAddress?: string;
  guestModeAllowed?: boolean;
  customValue?: string;
  description?: string;
}

export interface GetAllServerConfigsRequest {
  page?: number;
  pageSize?: number;
  os?: ClientOS;
  state?: ServerState;
  searchTerm?: string;
}

export interface GetAllServerConfigsResponse {
  configs: ServerConfig[];
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

export interface CreateServerConfigRequest {
  os: ClientOS;
  version: string;
  state?: ServerState;
  gameServerAddress: string;
  gameServerPort?: number;
  patchServerAddress: string;
  guestModeAllowed?: boolean;
  maintenanceMessage?: string;
  externalUrl?: string;
  customValue?: string;
  description?: string;
}

export interface CreateServerConfigResponse {
  id: number;
  message: string;
}
