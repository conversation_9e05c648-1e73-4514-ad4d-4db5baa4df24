// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { GetAllServerConfigsRequest, GetAllServerConfigsResponse, ClientOS, ServerState } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

export = async (req: RequestAs<GetAllServerConfigsRequest>, res: ResponseAs<GetAllServerConfigsResponse>) => {
  const { page, pageSize, os, state, searchTerm } = req.body;

  mlog.info('[RX] /getAllServerConfigs', {
    page,
    pageSize,
    os,
    state,
    searchTerm: searchTerm?.substring(0, 50) // 로그에는 일부만 출력
  });

  try {
    // 입력 검증
    if (page !== undefined && (page < 1 || !Number.isInteger(page))) {
      throw new MError('Invalid page number (must be positive integer)', MErrorCode.GATE_INVALID_REQUEST);
    }

    if (pageSize !== undefined && (pageSize < 1 || pageSize > 100 || !Number.isInteger(pageSize))) {
      throw new MError('Invalid page size (must be 1-100)', MErrorCode.GATE_INVALID_REQUEST);
    }

    if (os && !Object.values(ClientOS).includes(os as ClientOS)) {
      throw new MError('Invalid OS value', MErrorCode.GATE_INVALID_OS);
    }

    if (state && !Object.values(ServerState).includes(state as ServerState)) {
      throw new MError('Invalid state value', MErrorCode.GATE_INVALID_STATE);
    }

    if (searchTerm && searchTerm.length > 100) {
      throw new MError('Search term too long (max 100 characters)', MErrorCode.GATE_INVALID_REQUEST);
    }

    const result = await serverInfoService.getAllServerConfigs({
      page,
      pageSize,
      os: os as ClientOS,
      state: state as ServerState,
      searchTerm
    });

    mlog.info('[TX] /getAllServerConfigs', {
      count: result.configs.length,
      totalCount: result.pagination.totalCount,
      currentPage: result.pagination.currentPage,
      totalPages: result.pagination.totalPages
    });

    res.json(result);
  } catch (error) {
    mlog.error('Error in getAllServerConfigs', error);

    if (error instanceof MError) {
      throw error;
    }

    throw new MError(error.message, MErrorCode.GATE_GET_ALL_SERVER_CONFIGS_ERROR, undefined, error.stack);
  }
};
