// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { ServerConfig } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

interface GetAllServerConfigsResponse {
  configs: ServerConfig[];
}

export = async (req: RequestAs<{}>, res: ResponseAs<GetAllServerConfigsResponse>) => {
  mlog.info('[RX] /getAllServerConfigs');

  try {
    const configs = await serverInfoService.getAllServerConfigs();

    mlog.info('[TX] /getAllServerConfigs', { count: configs.length });

    res.json({ configs });
  } catch (error) {
    mlog.error('Error in getAllServerConfigs', error);
    
    if (error instanceof MError) {
      throw error;
    }
    
    throw new MError(error.message, MErrorCode.GATE_GET_ALL_SERVER_CONFIGS_ERROR, undefined, error.stack);
  }
};
