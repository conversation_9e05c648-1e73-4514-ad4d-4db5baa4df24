{"level":"info","message":"updating population for Fri Aug 01 2025 19:00:00 GMT+0900 (Korean Standard Time)","timestamp":"2025-08-01T10:00:00.006Z"}
{"level":"info","message":"reloading nation population","timestamp":"2025-08-01T10:00:00.007Z"}
{"sendTries":0,"sendCompleted":0,"sendFailed":0,"memUsage":{"sysTotal":"32004.36 MB","sysFree":"25963.79 MB","rss":"591.32 MB","heapTotal":"430.16 MB","heapUsed":"417.68 MB","external":"25650.85 MB"},"level":"info","message":"updateLobbydPingMonitor in 60000 sec","timestamp":"2025-08-01T10:00:40.899Z"}
{"count":0,"guildIds":[],"level":"info","message":"current-guild-raids-snapshot","timestamp":"2025-08-01T10:01:00.808Z"}
{"sendTries":0,"sendCompleted":0,"sendFailed":0,"memUsage":{"sysTotal":"32004.36 MB","sysFree":"25954.82 MB","rss":"591.67 MB","heapTotal":"430.16 MB","heapUsed":"417.95 MB","external":"25650.85 MB"},"level":"info","message":"updateLobbydPingMonitor in 60000 sec","timestamp":"2025-08-01T10:01:40.900Z"}
{"count":0,"guildIds":[],"level":"info","message":"current-guild-raids-snapshot","timestamp":"2025-08-01T10:02:01.820Z"}
{"sendTries":0,"sendCompleted":0,"sendFailed":0,"memUsage":{"sysTotal":"32004.36 MB","sysFree":"25952.3 MB","rss":"591.21 MB","heapTotal":"430.16 MB","heapUsed":"417.75 MB","external":"25650.85 MB"},"level":"info","message":"updateLobbydPingMonitor in 60000 sec","timestamp":"2025-08-01T10:02:40.901Z"}
{"level":"info","message":"[!] server is stopping: type=lobbyd, signal=SIGINT","timestamp":"2025-08-01T10:02:44.313Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T10:02:44.313Z"}
{"level":"info","message":"[TcpClient] client socket end.","timestamp":"2025-08-01T10:02:44.889Z"}
{"level":"error","message":"[TcpClient] client socket errorterminated!","stack":"Error: terminated!\n    at TcpClient.cleanupSocket (/mnt/c/work/uwo/game/server/node/src/tcplib/client/tcp-client.ts:89:27)\n    at Socket.<anonymous> (/mnt/c/work/uwo/game/server/node/src/tcplib/client/tcp-client.ts:417:14)\n    at Socket.emit (node:events:513:28)\n    at Socket.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1358:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","timestamp":"2025-08-01T10:02:44.903Z"}
{"level":"info","message":"[TcpClient] client socket closed.","timestamp":"2025-08-01T10:02:44.903Z"}
{"url":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"disconnected from oceand:","timestamp":"2025-08-01T10:02:44.903Z"}
{"level":"warn","message":"[TcpClient] try reconnect after 1000 ms.","timestamp":"2025-08-01T10:02:44.904Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T10:02:44.908Z"}
{"level":"info","message":"API server closed.","timestamp":"2025-08-01T10:02:44.909Z"}
{"level":"info","message":"Socket server closed.","timestamp":"2025-08-01T10:02:44.909Z"}
{"loggedIn":0,"loggingOut":0,"level":"info","message":"Waiting for termination...","timestamp":"2025-08-01T10:02:44.909Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:44.909Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:44.910Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:44.910Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:44.911Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:44.911Z"}
{"level":"info","message":"redis pool (user-redis) destroyed","timestamp":"2025-08-01T10:02:44.912Z"}
{"level":"info","message":"redis pool (town-redis) destroyed","timestamp":"2025-08-01T10:02:44.913Z"}
{"name":"user-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.913Z"}
{"name":"user-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.913Z"}
{"level":"info","message":"redis pool (guild-redis) destroyed","timestamp":"2025-08-01T10:02:44.913Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.915Z"}
{"level":"info","message":"redis pool (auction-redis-1) destroyed","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.915Z"}
{"name":"town-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.916Z"}
{"level":"info","message":"redis pool (auction-redis-2) destroyed","timestamp":"2025-08-01T10:02:44.916Z"}
{"name":"guild-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.916Z"}
{"name":"guild-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.916Z"}
{"level":"info","message":"redis pool (auction-redis-3) destroyed","timestamp":"2025-08-01T10:02:44.916Z"}
{"name":"auction-redis-1","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.916Z"}
{"name":"auction-redis-1","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.916Z"}
{"level":"info","message":"redis pool (auction-redis-4) destroyed","timestamp":"2025-08-01T10:02:44.917Z"}
{"name":"auction-redis-2","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.917Z"}
{"name":"auction-redis-2","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.917Z"}
{"level":"info","message":"redis pool (auction-redis-5) destroyed","timestamp":"2025-08-01T10:02:44.917Z"}
{"name":"auction-redis-3","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.917Z"}
{"name":"auction-redis-3","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.917Z"}
{"level":"info","message":"redis pool (auction-redis-6) destroyed","timestamp":"2025-08-01T10:02:44.917Z"}
{"name":"auction-redis-4","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.918Z"}
{"name":"auction-redis-4","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.918Z"}
{"level":"info","message":"redis pool (arena-redis) destroyed","timestamp":"2025-08-01T10:02:44.918Z"}
{"name":"auction-redis-5","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.918Z"}
{"name":"auction-redis-5","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.918Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T10:02:44.918Z"}
{"name":"auction-redis-6","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.918Z"}
{"name":"auction-redis-6","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.918Z"}
{"level":"info","message":"redis pool (raid-redis) destroyed","timestamp":"2025-08-01T10:02:44.919Z"}
{"name":"arena-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.919Z"}
{"name":"arena-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.919Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.920Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.920Z"}
{"name":"raid-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.920Z"}
{"name":"raid-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.920Z"}
{"level":"error","message":"An error is undefined at [close] event of amqpConn.","timestamp":"2025-08-01T10:02:44.950Z"}
{"level":"info","message":"[TcpClient] client socket end.","timestamp":"2025-08-01T10:02:44.950Z"}
{"level":"error","message":"[TcpClient] client socket errorterminated!","stack":"Error: terminated!\n    at TcpClient.cleanupSocket (/mnt/c/work/uwo/game/server/node/src/tcplib/client/tcp-client.ts:89:27)\n    at Socket.<anonymous> (/mnt/c/work/uwo/game/server/node/src/tcplib/client/tcp-client.ts:417:14)\n    at Socket.emit (node:events:513:28)\n    at Socket.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1358:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","timestamp":"2025-08-01T10:02:44.950Z"}
{"level":"info","message":"[TcpClient] client socket closed.","timestamp":"2025-08-01T10:02:44.950Z"}
{"url":"http://DESKTOP-2FFOGVN:11100","level":"info","message":"disconnected from saild:","timestamp":"2025-08-01T10:02:44.951Z"}
{"level":"warn","message":"[TcpClient] try reconnect after 1000 ms.","timestamp":"2025-08-01T10:02:44.951Z"}
{"level":"info","message":"redis pool (battle-log-redis) destroyed","timestamp":"2025-08-01T10:02:44.951Z"}
{"name":"battle-log-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.951Z"}
{"name":"battle-log-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.951Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T10:02:44.951Z"}
{"level":"info","message":"redis pool (global-battle-log-redis) destroyed","timestamp":"2025-08-01T10:02:44.952Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.952Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.952Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-01T10:02:44.952Z"}
{"name":"global-battle-log-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.952Z"}
{"name":"global-battle-log-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.953Z"}
{"level":"info","message":"redis pool (ranking-redis) destroyed","timestamp":"2025-08-01T10:02:44.953Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.953Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.953Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.953Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.953Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.953Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.954Z"}
{"level":"info","message":"redis pool (blind-bid-redis) destroyed","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:44.954Z"}
{"name":"ranking-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:44.954Z"}
