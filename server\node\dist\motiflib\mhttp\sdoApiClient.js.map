{"version": 3, "file": "sdoApiClient.js", "sourceRoot": "", "sources": ["../../../src/motiflib/mhttp/sdoApiClient.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;;AAG/E,mDAA2B;AAC3B,mDAAgD;AAEhD,wDAA2B;AAC3B,8CAA6C;AAE7C,qDAA6B;AAE7B,MAAa,gBAAgB;CA6B5B;AA7BD,4CA6BC;AAED,MAAa,YAAa,SAAQ,6BAAa;IAK7C;QACE,KAAK,EAAE,CAAC;QALF,gBAAW,GAAW,CAAC,CAAC;IAMhC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,OAAgB;;QACpC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7B,IAAI,CAAA,MAAA,eAAK,CAAC,GAAG,0CAAE,QAAQ,CAAC,QAAQ,MAAK,IAAI,EAAE;YACzC,cAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;SAClE;aAAM;YACL,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;IACH,CAAC;IAEO,YAAY;QAClB,MAAM,aAAa,GAAG,kBAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAEpD,IAAI,CAAC,QAAQ,GAAG,kBAAG,CAAC,OAAO,CAAC,2BAA2B,EAAE;YACvD,gBAAgB,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC;YACrC,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;YACzD,YAAY,EAAE,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;YAClC,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACrC,cAAc,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC;SAC7B,CAAC,CAAA;QAEF,IAAI,CAAC,sBAAsB,GAAG,kBAAG,CAAC,QAAQ,CACxC,MAAM,EACN,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,aAAa,KAAK,eAAe,EAAE;gBACrC,cAAI,CAAC,IAAI,CAAC,oCAAoC,aAAa,EAAE,CAAC,CAAC;aAChE;QACH,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,eAAK,CAAC,GAAG,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;SACxE;QAED,MAAM,GAAG,GAAG,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QACnC,MAAM,OAAO,GAAG,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrF,cAAI,CAAC,IAAI,CAAC,yBAAyB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAE3E,IAAA,yBAAY,EAAC,UAAU,EAAE,GAAG,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC7B,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAC1C,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;YACxB,KAAK,EAAE,eAAK,CAAC,GAAG,CAAC,KAAK;YACtB,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,QAAQ;SACpB,EAAE,eAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE3B,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,eAAK,CAAC,GAAG,CAAC,KAAK;YACtB,SAAS,EAAE,SAAS;YACpB,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,QAAQ;YACnB,IAAI,EAAE,OAAO;SACd,CAAA;QAED,MAAM,IAAI,GAAG,kBAAkB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;QACnE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,GAAG,CAAmB,IAAI,CAAC,CAAC;QACxD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,QAAiB,KAAK;QAChE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,eAAyB,EACzB,YAAwD,EACxD,QAAgB;QAEhB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,2BAA2B,CAC/B,cAAwB,EACxB,WAAgD;QAEhD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAc,EAAE,WAAmB,EAAE,KAAa;QAC/D,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,IAAY;QACnC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,OAAe,EACf,OAAe;IAEjB,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,GAAW,EAAE,cAAsB;IACrE,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAW;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,gBAAwB;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,gBAAwB;QACtD,OAAO;QACP,OAAO;YACL,IAAI,EAAE,gBAAgB;YACtB,wBAAwB,EAAE,EAAE;YAC5B,0BAA0B,EAAE,EAAE;SAC/B,CAAA;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;IAID,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,GAAsB;IAClE,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,WAAmB;QAC/C,OAAO,EACN,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,MAA2B;QACjD,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACxB,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAC/B,CAAC;IAEO,IAAI,CAAC,MAA2B,EAAE,SAAiB;QACzD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;YAC5B,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACrC;QAED,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAEO,GAAG,CAAC,KAAa;QACvB,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QACtC,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;CACF;AA9MD,oCA8MC;AAED,SAAgB,UAAU,CAAC,MAAc,EAAE,OAAe,EAAE,MAAM,GAAG,EAAE;IACrE,MAAM,KAAK,GAAG,GAAG,MAAM,IAAI,OAAO,EAAE,CAAC;IAErC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrE,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAG,qBAAqB;IAC3D,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAE,YAAY;IAClD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAM,iBAAiB;AACzD,CAAC;AARD,gCAQC"}