// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { UpdateServerStateRequest, ClientOS, ServerState } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

export = async (req: RequestAs<UpdateServerStateRequest>, res: ResponseAs<{}>) => {
  const { os, version, state }: UpdateServerStateRequest = req.body;

  mlog.info('[RX] /updateServerState', { body: req.body });

  try {
    // 입력 검증
    if (!os || !version || !state) {
      throw new MError('Missing required parameters: os, version, state', MErrorCode.GATE_INVALID_REQUEST);
    }

    // OS 값 검증
    if (!Object.values(ClientOS).includes(os as ClientOS)) {
      throw new MError('Invalid OS value', MErrorCode.GATE_INVALID_OS);
    }

    // 상태 값 검증
    if (!Object.values(ServerState).includes(state as ServerState)) {
      throw new MError('Invalid state value', MErrorCode.GATE_INVALID_STATE);
    }

    // 버전 형식 검증
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(version)) {
      throw new MError('Invalid version format', MErrorCode.GATE_INVALID_VERSION);
    }

    await serverInfoService.updateServerState({ 
      os: os as ClientOS, 
      version, 
      state: state as ServerState 
    });

    mlog.info('[TX] /updateServerState', { os, version, state });

    res.json({});
  } catch (error) {
    mlog.error('Error in updateServerState', error);
    
    if (error instanceof MError) {
      throw error;
    }
    
    throw new MError(error.message, MErrorCode.GATE_UPDATE_SERVER_STATE_ERROR, undefined, error.stack);
  }
};
