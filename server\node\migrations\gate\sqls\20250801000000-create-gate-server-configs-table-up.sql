-- gated 서버 설정 테이블 생성
CREATE TABLE g_server_configs (
  id INT NOT NULL AUTO_INCREMENT,
  os ENUM('windows', 'android', 'ios') NOT NULL,
  version VARCHAR(20) NOT NULL,
  state ENUM('online', 'offline', 'recommended_update', 'maintenance', 'access_denied', 'patch_allowed') NOT NULL DEFAULT 'online',
  gameServerAddress VARCHAR(255) NOT NULL,
  gameServerPort INT NOT NULL DEFAULT 8080,
  patchServerAddress VARCHAR(255) NOT NULL,
  guestModeAllowed TINYINT NOT NULL DEFAULT 1,
  maintenanceMessage TEXT NULL,
  externalUrl VARCHAR(500) NULL,
  customValue TEXT NULL,
  description TEXT NULL,
  createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY UQ_g_server_configs__os_version (os, version),
  INDEX IDX_g_server_configs__os (os),
  INDEX IDX_g_server_configs__state (state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 기본 설정 데이터 삽입
INSERT INTO g_server_configs (os, version, state, gameServerAddress, gameServerPort, patchServerAddress, guestModeAllowed, description) VALUES
('windows', '1.0.0', 'online', 'game.uwo.com', 8080, 'patch.uwo.com', 1, 'Windows 기본 설정'),
('android', '1.0.0', 'online', 'game.uwo.com', 8080, 'patch.uwo.com', 1, 'Android 기본 설정'),
('ios', '1.0.0', 'online', 'game.uwo.com', 8080, 'patch.uwo.com', 1, 'iOS 기본 설정');
