{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-01T07:02:20.082Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:02:20.083Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:02:20.151Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:02:20.158Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:02:20.159Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-01T07:02:20.161Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T07:02:20.162Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:02:20.163Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:02:20.163Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-01T07:02:20.163Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:02:20.164Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:02:20.164Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T07:02:20.164Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:02:20.165Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:02:20.165Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:02:20.165Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:02:20.165Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-01T07:02:20.166Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-01T07:02:20.166Z"}
{"environment":"development","type":"authd","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:02:23.123Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:02:51.831Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:02:53.843Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:02:53.844Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:02:53.845Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:02:53.846Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:02:54.873Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:02:54.875Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:02:54.876Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:02:54.883Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:02:54.884Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:02:54.900Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:02:54.989Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.012Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.031Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.045Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.060Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.073Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.093Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.125Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.143Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:55.163Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:02:55.252Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:02:55.253Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:02:55.258Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:02:55.358Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:02:55.360Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:02:55.361Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:02:55.361Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:02:55.364Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:02:55.365Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:02:55.365Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:02:55.365Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:02:55.369Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:02:55.370Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:02:55.371Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:02:55.371Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:02:55.372Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:02:55.375Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:02:55.375Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-01T07:02:56.312Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:56.312Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:56.313Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-01T07:02:56.313Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-01T07:02:56.358Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-01T07:02:56.358Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-01T07:02:56.370Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-01T07:02:56.371Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-01T07:02:56.382Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-01T07:02:56.382Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-01T07:02:56.393Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-01T07:02:56.394Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.512Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.543Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.586Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.636Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.707Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.784Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.872Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:56.965Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.006Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-01T07:02:57.022Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.032Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.126Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.152Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.181Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.201Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.806Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.839Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.868Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.909Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.929Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:57.966Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:58.003Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:58.106Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:58.148Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:58.169Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:58.194Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-01T07:02:58.227Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:02:58.230Z"}
{"url":"/getWorldStates","status":"200","response-time":"85.941","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:00.714Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.699","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:06.145Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.105","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:11.646Z"}
{"body":{"platform":2,"gnidSessionToken":"8149361**********1754031794","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":16185,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-01T07:03:16.556Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-01T07:03:16.570Z"}
{"url":"/getWorlds","status":"200","response-time":"59.764","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:16.571Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.868","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:16.948Z"}
{"body":{"platform":2,"sessionToken":"8149361**********1754031794","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-01T07:03:17.766Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:03:18&endpointip=127.0.0.1&endpointport=0&guid=f4f30e44f7784b9cb026e23e9d555cbd&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=d523bcecdd3fd82f877f7ae83bb2a545","timestamp":"2025-08-01T07:03:18.156Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:03:18.476Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라마바","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8149361**********1754031794","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"level":"info","message":"loginDbResult","timestamp":"2025-08-01T07:03:18.486Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-01T07:03:18.488Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"f7e8069da6f64052b15ec1ac9802735d1427fa60","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-01T07:03:18.489Z"}
{"url":"/login","status":"200","response-time":"768.626","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:18.489Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"f7e8069da6f64052b15ec1ac9802735d1427fa60","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-01T07:03:18.565Z"}
{"url":"/enterWorld","status":"200","response-time":"7.271","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:18.572Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-01T07:03:18.659Z"}
{"url":"/getUserNames","status":"200","response-time":"1.187","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:18.660Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:03:18.681Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.976","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:03:18.684Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-01T07:07:51.698Z"}
{"level":"verbose","message":"request: /useroffline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:07:51&endpointip=127.0.0.1&endpointport=0&guid=275a826bd3c643a79cd41684bdf69108&userid=**********&characterid=**********&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=f871fa4e7b59ab1f7dcb8f28d8ed5cf8","timestamp":"2025-08-01T07:07:51.699Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"baf654f2f635848550881ffa2d5ee39250dfd385","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":1,"level":"info","message":"/enterWorld","timestamp":"2025-08-01T07:07:51.970Z"}
{"accountId":"**********","userId":1000,"lastLobbyAppId":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/user is still online","timestamp":"2025-08-01T07:07:51.974Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-01T07:07:51.974Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-01T07:07:51.976Z"}
{"url":"/enterWorld","status":"200","response-time":"11.318","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:07:51.982Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-01T07:07:52.023Z"}
{"url":"/getUserNames","status":"200","response-time":"0.691","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:07:52.024Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:07:52.034Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.844","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:07:52.036Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:07:52.126Z"}
{"url":"/logout","status":"200","response-time":"433.459","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:07:52.131Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:07:52.133Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.810","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:07:52.135Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-01T07:12:18.007Z"}
{"level":"verbose","message":"request: /useroffline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:12:18&endpointip=127.0.0.1&endpointport=0&guid=cada145394014aef960d1fd539c03a85&userid=**********&characterid=**********&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=5f3268e15ca5f13e1de1458957daffd5","timestamp":"2025-08-01T07:12:18.007Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:12:19.495Z"}
{"url":"/logout","status":"200","response-time":"1490.861","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:12:19.497Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:12:19.499Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.540","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:12:19.500Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-01T07:12:29.323Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:12:29.323Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:12:29.848Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:12:29.850Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:12:29.852Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-01T07:12:29.853Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T07:12:29.853Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:12:29.854Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:12:29.854Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-01T07:12:29.854Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:12:29.854Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:12:29.855Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T07:12:29.855Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:12:29.855Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:12:29.855Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:12:29.856Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:12:29.856Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-01T07:12:29.856Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-01T07:12:29.856Z"}
{"environment":"development","type":"authd","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:20:45.406Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":6},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:21:13.958Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:21:15.592Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:21:15.593Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:21:15.593Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:21:15.594Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:21:19.191Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:21:19.193Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:21:19.194Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:21:19.201Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:21:19.202Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:21:19.217Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:21:19.308Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.332Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.349Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.362Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.379Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.393Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.413Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.434Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.452Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:19.473Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:21:19.575Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:21:19.576Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:21:19.582Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:21:19.685Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:21:19.688Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:21:19.689Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:21:19.689Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:21:19.692Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:21:19.693Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:21:19.693Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:21:19.693Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:21:19.697Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:21:19.698Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:21:19.698Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:21:19.699Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:21:19.700Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:21:19.702Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:21:19.702Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-01T07:21:20.357Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:20.358Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:20.359Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-01T07:21:20.359Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-01T07:21:20.447Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-01T07:21:20.447Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-01T07:21:20.454Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-01T07:21:20.454Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-01T07:21:20.462Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-01T07:21:20.462Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-01T07:21:20.469Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-01T07:21:20.469Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.543Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.565Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.606Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.643Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.683Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.723Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.768Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.843Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.872Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-01T07:21:20.882Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.886Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.938Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.947Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.964Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:20.978Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.377Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.398Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.419Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.436Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.446Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.460Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.482Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.543Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.563Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.572Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:21.596Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-01T07:21:21.617Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:21:21.620Z"}
{"layoutVersion":7,"level":"verbose","message":"sync config to...","timestamp":"2025-08-01T07:21:21.621Z"}
{"url":"/getWorldStates","status":"200","response-time":"83.918","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:23.375Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.994","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:28.786Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.931","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:34.284Z"}
{"url":"/getWorldStates","status":"200","response-time":"7.171","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:36.844Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.901","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:42.354Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.090","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:47.866Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.891","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:53.386Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.736","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:21:58.855Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.777","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:04.364Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.632","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:06.893Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.331","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:12.396Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.487","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:17.892Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.379","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:23.403Z"}
{"url":"/getWorldStates","status":"200","response-time":"11.415","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:28.923Z"}
{"body":{"platform":2,"gnidSessionToken":"8076376**********1754032950","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":100359,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-01T07:22:33.219Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-01T07:22:33.233Z"}
{"url":"/getWorlds","status":"200","response-time":"64.430","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:33.234Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.340","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:34.195Z"}
{"body":{"platform":2,"sessionToken":"8076376**********1754032950","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-01T07:22:35.727Z"}
{"url":"/getWorldStates","status":"200","response-time":"6.362","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:36.965Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:22:37&endpointip=127.0.0.1&endpointport=0&guid=8e39e68b338b44098fb55b2980f31cc7&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=8239782bccc2f34a2c2140411cb9400e","timestamp":"2025-08-01T07:22:37.454Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:22:38.688Z"}
{"loginDbResult":{"isOnline":0,"worlds":[],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":true},"reqBody":{"platform":2,"sessionToken":"8076376**********1754032950","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"level":"info","message":"loginDbResult","timestamp":"2025-08-01T07:22:38.699Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-01T07:22:38.700Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"37941093c509071264672e7e43c4bc26e5e78109","isNewUser":true,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-01T07:22:38.702Z"}
{"url":"/login","status":"200","response-time":"3026.982","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:38.703Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"37941093c509071264672e7e43c4bc26e5e78109","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-01T07:22:38.777Z"}
{"accountId":"**********","pubIdForPubIdCreating":"****************","worldId":"UWO-GL-01","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","level":"info","message":"taEnterWorld. create pub id","timestamp":"2025-08-01T07:22:38.781Z"}
{"url":"/enterWorld","status":"200","response-time":"13.085","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:38.790Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-01T07:22:38.869Z"}
{"url":"/getUserNames","status":"200","response-time":"0.928","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:38.870Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:22:38.896Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.223","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:38.898Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.731","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:42.270Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.605","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:47.994Z"}
{"userId":1000,"name":"가나다라마바","worldId":"UWO-GL-01","level":"info","message":"/changeUserName","timestamp":"2025-08-01T07:22:49.251Z"}
{"url":"/changeUserName","status":"200","response-time":"9.118","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:22:49.260Z"}
{"userId":1000,"nationCmsId":********,"level":"info","message":"/changeUserNation","timestamp":"2025-08-01T07:23:05.346Z"}
{"url":"/changeUserNation","status":"200","response-time":"6.722","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:23:05.353Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-01T07:26:33.023Z"}
{"level":"verbose","message":"request: /useroffline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:26:33&endpointip=127.0.0.1&endpointport=0&guid=54ffec7a371e4f73a6bc9b26b99d26f5&userid=**********&characterid=**********&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=2451fd32a4d072083eacf84e1dd163c8","timestamp":"2025-08-01T07:26:33.023Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:26:33.459Z"}
{"url":"/logout","status":"200","response-time":"443.113","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:33.465Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:26:33.468Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.885","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:33.469Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.643","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:37.130Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.439","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:42.647Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.606","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:48.157Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.610","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:53.669Z"}
{"body":{"platform":2,"gnidSessionToken":"8874900**********1754033213","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":19159,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-01T07:26:56.341Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-01T07:26:56.345Z"}
{"url":"/getWorlds","status":"200","response-time":"54.200","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:56.345Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.433","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:26:58.935Z"}
{"body":{"platform":2,"sessionToken":"8874900**********1754033213","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-01T07:27:02.260Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:27:02&endpointip=127.0.0.1&endpointport=0&guid=69027551065641458bda6827da833f74&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=ebab47b310c464db3369d736d26737d8","timestamp":"2025-08-01T07:27:02.561Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:27:02.647Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라마바","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8874900**********1754033213","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"level":"info","message":"loginDbResult","timestamp":"2025-08-01T07:27:02.655Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-01T07:27:02.657Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"96ec4f96020c06e2c1c07aa9fb132cbd2796b858","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-01T07:27:02.658Z"}
{"url":"/login","status":"200","response-time":"449.369","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:27:02.659Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"96ec4f96020c06e2c1c07aa9fb132cbd2796b858","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-01T07:27:02.727Z"}
{"url":"/enterWorld","status":"200","response-time":"6.781","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:27:02.734Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-01T07:27:02.766Z"}
{"url":"/getUserNames","status":"200","response-time":"0.688","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:27:02.766Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:27:02.775Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.514","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:27:02.777Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.319","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:27:02.136Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-01T07:50:41.538Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:50:41.538Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:50:42.344Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:50:42.348Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:50:42.348Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-01T07:50:42.349Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T07:50:42.350Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:50:42.350Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:50:42.350Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-01T07:50:42.350Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:50:42.351Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:50:42.351Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T07:50:42.351Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:50:42.351Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:50:42.352Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:50:42.352Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:50:42.352Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-01T07:50:42.352Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-01T07:50:42.353Z"}
{"environment":"development","type":"authd","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:50:42.670Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:51:13.367Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:51:15.046Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:51:15.047Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:51:15.048Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:51:15.049Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:51:18.821Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:51:18.822Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:51:18.823Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:51:18.830Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:51:18.831Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:51:18.846Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:51:18.947Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:18.969Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:18.989Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.009Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.027Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.041Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.059Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.077Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.093Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:19.112Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:51:19.201Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:51:19.202Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:51:19.208Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:51:19.310Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:51:19.312Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:51:19.313Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:51:19.313Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:51:19.316Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:51:19.317Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:51:19.317Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:51:19.317Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:51:19.322Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:51:19.323Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:51:19.323Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:51:19.324Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:51:19.325Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:51:19.327Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:51:19.327Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-01T07:51:20.245Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:20.245Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:20.246Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-01T07:51:20.246Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-01T07:51:20.287Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-01T07:51:20.287Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-01T07:51:20.297Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-01T07:51:20.297Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-01T07:51:20.307Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-01T07:51:20.307Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-01T07:51:20.317Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-01T07:51:20.318Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.428Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.464Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.504Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.551Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.605Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.650Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.735Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.799Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.835Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-01T07:51:20.847Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.852Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.882Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.942Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.972Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:20.993Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.490Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.515Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.546Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.575Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.591Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.613Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.646Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.699Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.755Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.772Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:21.793Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-01T07:51:21.819Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:51:21.822Z"}
{"url":"/getWorldStates","status":"200","response-time":"79.587","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:22.676Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.593","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:28.099Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.211","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:33.602Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.486","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:39.116Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.047","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:41.621Z"}
{"body":{"platform":2,"gnidSessionToken":"8840576**********1754034702","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":25373,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-01T07:51:45.695Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-01T07:51:45.708Z"}
{"url":"/getWorlds","status":"200","response-time":"69.682","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:45.709Z"}
{"body":{"platform":2,"sessionToken":"8840576**********1754034702","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-01T07:51:46.275Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.570","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:47.148Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:51:47&endpointip=127.0.0.1&endpointport=0&guid=cf16af8bc917419883cc34f39e5f4b77&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=c849e3d6c3e45028deda623918052dca","timestamp":"2025-08-01T07:51:47.872Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:51:48.228Z"}
{"loginDbResult":{"isOnline":1,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라마바","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8840576**********1754034702","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"level":"info","message":"loginDbResult","timestamp":"2025-08-01T07:51:48.237Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-01T07:51:48.239Z"}
{"accountId":"**********","userId":1000,"lastLobby":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/login user already online","timestamp":"2025-08-01T07:51:48.239Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-01T07:51:48.239Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-01T07:51:48.241Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"6e0ef76faf81bef4d85a3231b4445387dd8cae87","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-01T07:51:48.242Z"}
{"url":"/login","status":"200","response-time":"2009.694","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:48.243Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"6e0ef76faf81bef4d85a3231b4445387dd8cae87","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-01T07:51:48.324Z"}
{"accountId":"**********","userId":1000,"lastLobbyAppId":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/user is still online","timestamp":"2025-08-01T07:51:48.326Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-01T07:51:48.326Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-01T07:51:48.327Z"}
{"url":"/enterWorld","status":"200","response-time":"9.816","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:48.334Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-01T07:51:48.429Z"}
{"url":"/getUserNames","status":"200","response-time":"1.056","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:48.430Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-01T07:51:48.445Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.216","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:51:48.447Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-01T07:54:59.602Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:54:59.602Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:54:59.890Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:54:59.892Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:54:59.893Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-01T07:54:59.894Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T07:54:59.895Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:54:59.897Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:54:59.897Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-01T07:54:59.898Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:54:59.899Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:54:59.899Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T07:54:59.900Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:54:59.901Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:54:59.902Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:54:59.902Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:54:59.902Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-01T07:54:59.903Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-01T07:54:59.903Z"}
{"environment":"development","type":"authd","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:55:03.888Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:55:29.364Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:55:30.832Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:55:30.833Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:55:30.834Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:55:30.834Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:55:35.346Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:55:35.347Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:55:35.348Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:55:35.354Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:55:35.355Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:55:35.370Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:55:35.455Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.476Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.492Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.505Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.519Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.532Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.549Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.568Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.587Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:35.608Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:55:35.696Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:55:35.697Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:55:35.703Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:55:35.809Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:55:35.812Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:55:35.813Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:55:35.814Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:55:35.817Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:55:35.818Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:55:35.818Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:55:35.818Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:55:35.824Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:55:35.825Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:55:35.825Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:55:35.825Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:55:35.826Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:55:35.829Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:55:35.829Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-01T07:55:36.907Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:36.908Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:36.909Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-01T07:55:36.909Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-01T07:55:36.952Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-01T07:55:36.953Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-01T07:55:36.963Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-01T07:55:36.964Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-01T07:55:36.973Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-01T07:55:36.974Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-01T07:55:36.983Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-01T07:55:36.983Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.103Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.141Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.183Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.235Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.325Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.399Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.503Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.595Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.630Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-01T07:55:37.643Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.649Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.695Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.774Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.805Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.836Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.562Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.607Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.655Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.701Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.728Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.762Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.815Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.875Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.932Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.954Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:35.974Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-01T07:55:36.011Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:55:36.014Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-01T07:59:09.516Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:59:09.516Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:59:09.680Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:59:09.683Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:59:09.686Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-01T07:59:09.687Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T07:59:09.688Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:59:09.688Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:59:09.689Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-01T07:59:09.689Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T07:59:09.689Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:59:09.690Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:59:09.690Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-01T07:59:09.690Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-01T07:59:09.690Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:59:09.690Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:59:09.690Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:59:09.691Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:59:09.691Z"}
{"environment":"development","type":"authd","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:59:13.323Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:59:35.579Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:59:37.249Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:59:37.250Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:59:37.251Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:59:37.252Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:59:41.463Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:59:41.464Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:59:41.465Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:59:41.473Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:59:41.474Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:59:41.490Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:59:41.579Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.601Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.617Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.630Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.645Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.657Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.676Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.695Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.712Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:41.731Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:59:41.816Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:59:41.817Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:59:41.822Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:59:41.920Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:59:41.923Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:59:41.924Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:59:41.924Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:59:41.927Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:59:41.927Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:59:41.927Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:59:41.927Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:59:41.932Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:59:41.933Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:59:41.933Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:59:41.934Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:59:41.935Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:59:41.937Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:59:41.937Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-01T07:59:43.043Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:43.044Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:43.045Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-01T07:59:43.045Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-01T07:59:43.097Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-01T07:59:43.098Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-01T07:59:43.108Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-01T07:59:43.109Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-01T07:59:43.118Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-01T07:59:43.119Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-01T07:59:43.128Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-01T07:59:43.128Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.257Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.295Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.329Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.366Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.421Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.472Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.556Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.634Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.674Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-01T07:59:43.686Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.693Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.789Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.812Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.845Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:43.870Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.330Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.362Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.399Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.432Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.447Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.468Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.504Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.561Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.621Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.645Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:44.663Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-01T07:59:44.692Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:59:44.781Z"}
{"url":"/getWorldStates","status":"200","response-time":"88.822","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:59:44.784Z"}
{"body":{"platform":2,"gnidSessionToken":"8879972**********1754035183","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":16823,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-01T07:59:45.636Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-01T07:59:45.655Z"}
{"url":"/getWorlds","status":"200","response-time":"20.972","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:59:45.656Z"}
{"body":{"platform":2,"sessionToken":"8879972**********1754035183","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-01T07:59:46.346Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-01 16:59:47&endpointip=127.0.0.1&endpointport=0&guid=a6db744a892446d4909cdf63bf4cd7bb&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=39b126cbf8e57c7ad6e02997aba427f7","timestamp":"2025-08-01T07:59:47.697Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-01T07:59:48.048Z"}
{"loginDbResult":{"isOnline":1,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라마바","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8879972**********1754035183","revision":"bf713f9fa5138095473d6fffca702ffb7366bb7f","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false},"level":"info","message":"loginDbResult","timestamp":"2025-08-01T07:59:48.064Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-01T07:59:48.066Z"}
{"accountId":"**********","userId":1000,"lastLobby":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/login user already online","timestamp":"2025-08-01T07:59:48.067Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-01T07:59:48.067Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"kick published with no subscribiers","timestamp":"2025-08-01T07:59:48.068Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"81e2e0ce0fb81ccc39f5e21fe4f62cca3648f696","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-01T07:59:48.069Z"}
{"url":"/login","status":"200","response-time":"1771.042","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:59:48.069Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.563","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-01T07:59:49.912Z"}
