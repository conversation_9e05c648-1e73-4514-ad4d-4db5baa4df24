// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { SetMaintenanceMessageRequest, ClientOS } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

export = async (req: RequestAs<SetMaintenanceMessageRequest>, res: ResponseAs<{}>) => {
  const { os, version, maintenanceMessage, externalUrl }: SetMaintenanceMessageRequest = req.body;

  mlog.info('[RX] /setMaintenanceMessage', { 
    body: { 
      os, 
      version, 
      maintenanceMessage: maintenanceMessage?.substring(0, 100) + '...', // 로그에는 일부만 출력
      externalUrl 
    } 
  });

  try {
    // 입력 검증
    if (!os || !version || !maintenanceMessage) {
      throw new MError('Missing required parameters: os, version, maintenanceMessage', MErrorCode.GATE_INVALID_REQUEST);
    }

    // OS 값 검증
    if (!Object.values(ClientOS).includes(os as ClientOS)) {
      throw new MError('Invalid OS value', MErrorCode.GATE_INVALID_OS);
    }

    // 버전 형식 검증
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(version)) {
      throw new MError('Invalid version format', MErrorCode.GATE_INVALID_VERSION);
    }

    // 점검 메시지 길이 검증
    if (maintenanceMessage.length > 1000) {
      throw new MError('Maintenance message too long (max 1000 characters)', MErrorCode.GATE_MESSAGE_TOO_LONG);
    }

    // 외부 URL 형식 검증 (선택사항)
    if (externalUrl) {
      try {
        new URL(externalUrl);
      } catch {
        throw new MError('Invalid external URL format', MErrorCode.GATE_INVALID_URL);
      }
    }

    await serverInfoService.setMaintenanceMessage({ 
      os: os as ClientOS, 
      version, 
      maintenanceMessage,
      externalUrl 
    });

    mlog.info('[TX] /setMaintenanceMessage', { os, version });

    res.json({});
  } catch (error) {
    mlog.error('Error in setMaintenanceMessage', error);
    
    if (error instanceof MError) {
      throw error;
    }
    
    throw new MError(error.message, MErrorCode.GATE_SET_MAINTENANCE_MESSAGE_ERROR, undefined, error.stack);
  }
};
