{"version": 3, "file": "mconf.js", "sourceRoot": "", "sources": ["../../src/motiflib/mconf.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;AAE/E;;;GAGG;AACH,oDAA4B;AAC5B,4CAAoB;AACpB,kDAA0B;AAC1B,oDAAuB;AACvB,gDAAwB;AACxB,qCAA8C;AAC9C,4CAA6C;AAC7C,iEAA8D;AAC9D,uCAAuC;AAGvC,MAAM,oBAAoB,GAAG;IAC3B,MAAM,EAAE;QACN,aAAa;QACb,cAAc;QACd,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,gBAAgB;QAChB,WAAW;QACX,cAAc;QACd,YAAY;QACZ,kBAAkB;QAClB,YAAY;QACZ,WAAW;QACX,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,aAAa;QACb,cAAc;QACd,eAAe;KAChB;IACD,MAAM,EAAE;QACN,aAAa;QACb,cAAc;QACd,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;KAChB;IACD,KAAK,EAAE,CAAC,kBAAkB,EAAE,UAAU,EAAE,aAAa,CAAC;IACtD,MAAM,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,kBAAkB,EAAE,UAAU,EAAE,aAAa,CAAC;IACnF,KAAK,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC;IAC/D,OAAO,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,CAAC;CACpE,CAAC;AAEF,MAAM,oBAAoB,GAAG;IAC3B,MAAM,EAAE,CAAC,UAAU,CAAC;IACpB,MAAM,EAAE,CAAC,UAAU,CAAC;CACrB,CAAC;AAEF,SAAS,qBAAqB,CAAC,OAAe;IAC5C,MAAM,IAAI,eAAM,CAAC,0BAA0B,EAAE,mBAAU,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;AACvF,CAAC;AAiCD,MAAM,KAAK;IA0GT;QAuFA,SAAI,GAAG,gBAAM,CAAC,IAAI,CAAC;QAtFjB,MAAM,OAAO,GAAG,IAAA,oBAAU,GAAE,CAAC;QAE7B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAXD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,QAAQ,KAAK,eAAQ,CAAC,GAAG,CAAC;IACxC,CAAC;IAWD,MAAM,CAAC,MAA8B;QACnC,gBAAC,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxB,CAAC;IAED,iBAAiB,CAAC,WAAgB;QAChC,gBAAgB;QAChB,gBAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;QAErC,kBAAkB;QAClB,MAAM,eAAe,GAAG,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO;SACR;QACD,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,SAAS,EAAE;gBAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC;aAChC;SACF;QAED,MAAM,eAAe,GAAG,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,eAAe,EAAE;YACnB,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;gBACrC,IAAI,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;aACtC;SACF;IACH,CAAC;IAED,GAAG,CAAI,OAAe;QACpB,OAAO,gBAAM,CAAC,GAAG,CAAI,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,GAAG,CAAC,OAAe;QACjB,OAAO,gBAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED,cAAc,CAAC,SAAkB;QAC/B,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;QACrD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,IAAI,CAAC;SACb;QAED,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,MAAM,EAAE;YACrC,IAAI,WAAW,CAAC,EAAE,KAAK,OAAO,EAAE;gBAC9B,OAAO,WAAW,CAAC;aACpB;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB,CAAC,UAAkB;QACpC,IAAI,eAAe,GAAU,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACnD,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;oBAC/B,OAAO;iBACR;gBACD,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,SAAS;IACT,cAAc;QACZ,IAAI,KAAK,CAAC,QAAQ,KAAK,eAAQ,CAAC,IAAI,EAAE;YACpC,qBAAqB;YACrB,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrC,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;SAC1C;aAAM;YACL,OAAO,CAAC,CAAC;SACV;IACH,CAAC;CAGF;AAED,IAAI,KAAK,GAAU,IAAI,KAAK,EAAE,CAAC;AAC/B,gBAAC,CAAC,KAAK,CAAC,KAAK,EAAE,gBAAM,CAAC,CAAC;AAEvB,sBAAsB;AACtB,IAAI;IACF,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAA,2CAAoB,GAAE,CAAC,CAAC,CAAC;IACvG,MAAM,SAAS,GAAG,YAAE,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IACzD,MAAM,SAAS,GAAG,eAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACzC,gBAAC,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;CAC3B;AAAC,WAAM,GAAE;AAEV,kBAAe,KAAK,CAAC"}