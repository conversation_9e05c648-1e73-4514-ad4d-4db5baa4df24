"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sdoMakeNid = exports.SdoApiClient = exports.SdoLoginResponse = void 0;
const mlog_1 = __importDefault(require("../mlog"));
const baseApiClient_1 = require("./baseApiClient");
const ffi_napi_1 = __importDefault(require("ffi-napi"));
const finalizers_1 = require("../finalizers");
const mconf_1 = __importDefault(require("../mconf"));
class SdoLoginResponse {
}
exports.SdoLoginResponse = SdoLoginResponse;
class SdoApiClient extends baseApiClient_1.BaseApiClient {
    constructor() {
        super();
        this.loginNumber = 0;
    }
    init(baseUrl, timeout) {
        var _a;
        super.init(baseUrl, timeout);
        if (((_a = mconf_1.default.sdo) === null || _a === void 0 ? void 0 : _a.maskword.disabled) === true) {
            mlog_1.default.warn('maskword is disabled by mconf.sdo.maskword.disabled');
        }
        else {
            this.initMaskword();
        }
    }
    initMaskword() {
        const update_cbtype = ffi_napi_1.default.Function('void', ['int']);
        this.maskword = ffi_napi_1.default.Library('./maskword_cn/libmaskword', {
            'MaskWordErrMsg': ['string', ['int']],
            'InitMaskWord': ['int', ['string', 'int', update_cbtype]],
            'SearchUtf8': ['bool', ['string']],
            'ReplaceUtf8': ['string', ['string']],
            'StopMaskWord': ['void', []],
        });
        this.maskwordUpdateCallback = ffi_napi_1.default.Callback('void', ['int'], (result) => {
            const resultMessage = this.maskword.MaskWordErrMsg(result);
            if (resultMessage !== 'CDN no update') {
                mlog_1.default.info(`init maskword callback, result = ${resultMessage}`);
            }
        });
        if (!mconf_1.default.sdo) {
            throw new Error('mconf.sdo.maskword configuration is missing');
        }
        if (!mconf_1.default.sdo.maskword.url) {
            throw new Error('mconf.sdo.maskword.url configuration is missing');
        }
        if (!mconf_1.default.sdo.maskword.timeout) {
            throw new Error('mconf.sdo.maskword.timeout configuration is missing');
        }
        const url = mconf_1.default.sdo.maskword.url;
        const timeout = mconf_1.default.sdo.maskword.timeout;
        const result = this.maskword.InitMaskWord(url, timeout, this.maskwordUpdateCallback);
        mlog_1.default.info(`init maskword, result=${this.maskword.MaskWordErrMsg(result)}`);
        (0, finalizers_1.addFinalizer)('maskword', () => {
            this.maskword.StopMaskWord();
            this.maskwordUpdateCallback = undefined;
        });
    }
    async login(ticketId) {
        const timestamp = Math.floor(Date.now() / 1000);
        const sequence = this.loginNumber++;
        const signKey = this.sign({
            appid: mconf_1.default.sdo.appId,
            timestamp: timestamp,
            sequence: sequence,
            ticket_id: ticketId,
        }, mconf_1.default.sdo.appSecretKey);
        const queryParams = {
            appid: mconf_1.default.sdo.appId,
            timestamp: timestamp,
            sequence: sequence,
            ticket_id: ticketId,
            sign: signKey,
        };
        const path = `/v1/open/ticket${this.makeQueryParams(queryParams)}`;
        const response = await this.get(path);
        return response;
    }
    async isWorldInMaintenance(worldId, force = false) {
        return false;
    }
    async reportBulkMailResult(successUserList, failUserList, configId) {
        return true;
    }
    async reportAccountDeletionResult(successNidList, failNidList) {
        return true;
    }
    async getMails(userId, langCulture, level) {
        return [];
    }
    async hasBadWord(text) {
        if (!text || text.length === 0) {
            return false;
        }
        if (!this.maskword) {
            return false;
        }
        return this.maskword.SearchUtf8(text) === true;
    }
    async replaceBadWordText(text) {
        if (!text || text.length === 0) {
            return text;
        }
        if (!this.maskword) {
            return text;
        }
        return this.maskword.ReplaceUtf8(text);
    }
    async reportBadChat(reportUserId, targetUserId, reasonCd, chatMsg, addInfo) {
    }
    async changeNidForServerMigration(nid, toGameServerId) {
    }
    async revoke(nid) {
        return true;
    }
    async cancelRevoke(gnidSessionToken) {
        return true;
    }
    async reqWhiteServerIpList() {
        return [];
    }
    async getNidAndServerInfoByGnid(gnidSessionToken) {
        // TODO
        return {
            gnid: gnidSessionToken,
            registeredGameServerList: [],
            createdNidGameServerIdList: [],
        };
    }
    async getServerList() {
        // TODO
        return [];
    }
    async sendPushNotification(message, arg) {
    }
    async reportBadChatReasonList(langCulture) {
        return {};
    }
    makeQueryParams(params) {
        const pairs = [];
        for (const key in params) {
            pairs.push(`${key}=${params[key]}`);
        }
        return `?${pairs.join('&')}`;
    }
    sign(params, secretKey) {
        const sortedKeys = Object.keys(params).sort();
        const pairs = [];
        for (const key of sortedKeys) {
            pairs.push(`${key}=${params[key]}`);
        }
        const str = pairs.join('&') + secretKey;
        return this.md5(str);
    }
    md5(input) {
        const crypto = require('node:crypto');
        return crypto.createHash('md5').update(input).digest('hex');
    }
}
exports.SdoApiClient = SdoApiClient;
function sdoMakeNid(userId, worldId, digits = 16) {
    const input = `${userId}-${worldId}`;
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(input).digest('hex');
    const bigInt = BigInt('0x' + hash); // 16진 해시를 BigInt로 변환
    const decimal = bigInt.toString(10); // 10진수 문자열로
    return decimal.slice(0, digits); // 숫자만 추출 (앞 n자리)
}
exports.sdoMakeNid = sdoMakeNid;
//# sourceMappingURL=sdoApiClient.js.map