// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import express from 'express';
import bodyParser from 'body-parser';
import morgan from 'morgan';
import path from 'path';
import stoppable from 'stoppable';
import Container from 'typedi';

import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { DBConnPool } from '../mysqllib/pool';

// ----------------------------------------------------------------------------
// Global variables.
// ----------------------------------------------------------------------------
const publicGateApp = express();
const privateGateApp = express();
const publicGateServer = stoppable(require('http').createServer(publicGateApp));
const privateGateServer = stoppable(require('http').createServer(privateGateApp));

let isStopping = false;

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------
const gateReqLog = (tokens: any, req: any, res: any) => {
  return [
    tokens.method(req, res),
    tokens.url(req, res),
    tokens.status(req, res),
    tokens.res(req, res, 'content-length'), '-',
    tokens['response-time'](req, res), 'ms'
  ].join(' ');
};

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
export const stop = async () => {
  if (isStopping) {
    return;
  }
  isStopping = true;

  mlog.info('stopping gated server...');

  // Stop http servers.
  await new Promise<void>((resolve) => {
    publicGateServer.stop(() => {
      mlog.info('public gate server stopped');
      resolve();
    });
  });

  await new Promise<void>((resolve) => {
    privateGateServer.stop(() => {
      mlog.info('private gate server stopped');
      resolve();
    });
  });

  // Close database connections.
  const dbConnPool = Container.get(DBConnPool);
  await dbConnPool.destroy();

  mlog.info('gated server stopped');
};

export const start = async () => {
  try {
    mutil.initSentry();

    // Init mysql connection pool.
    const dbConnPool = Container.get(DBConnPool);
    await dbConnPool.init(mconf.mysqlGateDb);

    // listen public gate app
    {
      const bindAddress = mconf.publicApiService.bindAddress;
      const port = mconf.publicApiService.port;
      publicGateApp.use(morgan(gateReqLog));
      publicGateApp.use(bodyParser.json());
      publicGateApp.use(bodyParser.urlencoded({ extended: true }));
      mutil.registerHealthCheck(publicGateApp);
      mutil.registerGarbageCollector(publicGateApp);
      await dirAsApi.register(publicGateApp, path.join(__dirname, 'api', 'public'));

      publicGateApp.use(expressError.middleware);
      publicGateServer.listen(port, bindAddress, () => {
        mlog.info('start public gate server listening ...', { bindAddress, port });
      });
    }

    // listen private gate app
    {
      const bindAddress = mconf.privateApiService.bindAddress;
      const port = mconf.privateApiService.port;
      privateGateApp.use(morgan(gateReqLog));
      privateGateApp.use(bodyParser.json());
      mutil.registerHealthCheck(privateGateApp);
      await dirAsApi.register(privateGateApp, path.join(__dirname, 'api', 'private'));

      privateGateApp.use(expressError.middleware);
      privateGateServer.listen(port, bindAddress, () => {
        mlog.info('start private gate server listening ...', { bindAddress, port });
      });
    }

    mlog.info('gated server started successfully');
  } catch (error) {
    mlog.error('Failed to start gated server', error);
    throw error;
  }
};

// Handle graceful shutdown
process.on('SIGTERM', stop);
process.on('SIGINT', stop);
