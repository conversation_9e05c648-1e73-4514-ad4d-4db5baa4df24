"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
Object.defineProperty(exports, "__esModule", { value: true });
exports.SdoLogApiClient = void 0;
const baseApiClient_1 = require("./baseApiClient");
class SdoLogApiClient extends baseApiClient_1.BaseApiClient {
    constructor() {
        super();
    }
    init(baseUrl, timeout) {
        super.init(baseUrl, timeout);
    }
    saveCommonLog(data) {
        if (!data) {
            return Promise.resolve(false);
        }
        return Promise.resolve(true);
    }
    isTokenRefreshing() {
        return false;
    }
    async refresh() {
        return Promise.resolve();
    }
    refreshTokenIfExpired() {
        return Promise.resolve();
    }
}
exports.SdoLogApiClient = SdoLogApiClient;
//# sourceMappingURL=sdoLogApiClient.js.map