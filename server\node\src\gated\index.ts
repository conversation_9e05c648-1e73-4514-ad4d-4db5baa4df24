// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as server from './server';

// Set process name for configuration
process.name = 'gated';

// Start the server
server.start().catch((error) => {
  console.error('Failed to start gated server:', error);
  process.exit(1);
});
