{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-01T07:00:00.104Z"}
{"12000000":{"10000000":216,"10000001":337,"10000005":72,"10001065":289,"10001130":84},"12000100":{"10001011":93,"10001012":186,"10001024":93,"10001027":133,"10001054":93,"10001058":186,"10001082":93,"10001092":120},"12000200":{"10001006":116,"10001016":116,"10001036":433,"10001097":183,"10001125":150},"12000300":{},"12000301":{"10001071":1000},"12000302":{"10001033":740,"10001103":259},"12000303":{},"12000304":{},"12000400":{"10000005":428,"10001001":142,"10001017":142,"10001081":285},"12000500":{"10000000":122,"10000001":81,"10000002":244,"10000004":367,"10001056":183},"12000600":{"10001055":106,"10001062":106,"10001069":469,"10001072":106,"10001091":212},"12000700":{"10001013":121,"10001014":195,"10001016":109,"10001032":121,"10001045":280,"10001094":85,"10001095":85},"12000800":{"10001031":1000},"12000900":{"10001009":153,"10001026":76,"10001028":109,"10001035":76,"10001052":98,"10001059":98,"10001074":76,"10001077":76,"10001079":76,"10001090":76,"10001127":76},"12001000":{"10000004":223,"10001070":342,"10001097":118,"10001125":131,"10001128":184},"12001100":{"10001029":91,"10001042":346,"10001049":71,"10001063":91,"10001087":234,"10001100":91,"10001124":71},"12001200":{"10000002":250,"10000003":312,"10001016":296,"10001046":140},"12001300":{"10001022":200,"10001044":400,"10001053":200,"10001080":200},"12001301":{"10001008":145,"10001022":112,"10001053":145,"10001066":112,"10001068":225,"10001080":145,"10001086":112},"12001302":{},"12001303":{},"12001400":{"10001003":74,"10001010":180,"10001012":74,"10001018":74,"10001019":74,"10001040":148,"10001043":74,"10001067":74,"10001089":223},"12001500":{"10001013":333,"10001014":666},"12001501":{},"12001502":{},"12001503":{"10001032":500,"10001060":250,"10001068":250},"12001504":{"10001068":1000},"12001505":{"10001032":1000},"12001600":{"10000005":112,"10001029":516,"10001036":370},"12001700":{"10001021":132,"10001060":132,"10001071":75,"10001073":132,"10001093":132,"10001096":132,"10001101":132,"10001102":132},"12001701":{},"12001702":{},"12001703":{},"12001704":{},"12001705":{},"12001706":{"10001023":166,"10001061":500,"10001088":166,"10001126":166},"12001707":{},"12001708":{},"12001800":{"10001038":218,"10001078":281,"10001079":500},"12001801":{"10001015":92,"10001020":210,"10001025":210,"10001030":92,"10001057":92,"10001083":92,"10001084":210},"12001900":{"10001005":79,"10001047":79,"10001051":79,"10001075":79,"10001076":340,"10001085":79,"10001086":261},"12002000":{"10001004":93,"10001007":93,"10001037":93,"10001041":320,"10001048":186,"10001050":120,"10001098":93},"12010000":{},"12010100":{},"12010101":{},"12010102":{},"12010200":{},"12010300":{},"12010400":{},"12010500":{},"12010600":{},"12010700":{},"12010701":{},"12010800":{},"12010900":{"10001131":1000},"12011000":{},"12011100":{},"12011200":{},"12011300":{},"12011400":{},"12011401":{},"level":"info","message":"subscribe nation_share_rate_updated","timestamp":"2025-08-01T07:00:00.127Z"}
{"12000000":{"10000000":216,"10000001":337,"10000005":72,"10001065":289,"10001130":84},"12000100":{"10001011":93,"10001012":186,"10001024":93,"10001027":133,"10001054":93,"10001058":186,"10001082":93,"10001092":120},"12000200":{"10001006":116,"10001016":116,"10001036":433,"10001097":183,"10001125":150},"12000300":{},"12000301":{"10001071":1000},"12000302":{"10001033":740,"10001103":259},"12000303":{},"12000304":{},"12000400":{"10000005":428,"10001001":142,"10001017":142,"10001081":285},"12000500":{"10000000":122,"10000001":81,"10000002":244,"10000004":367,"10001056":183},"12000600":{"10001055":106,"10001062":106,"10001069":469,"10001072":106,"10001091":212},"12000700":{"10001013":121,"10001014":195,"10001016":109,"10001032":121,"10001045":280,"10001094":85,"10001095":85},"12000800":{"10001031":1000},"12000900":{"10001009":153,"10001026":76,"10001028":109,"10001035":76,"10001052":98,"10001059":98,"10001074":76,"10001077":76,"10001079":76,"10001090":76,"10001127":76},"12001000":{"10000004":223,"10001070":342,"10001097":118,"10001125":131,"10001128":184},"12001100":{"10001029":91,"10001042":346,"10001049":71,"10001063":91,"10001087":234,"10001100":91,"10001124":71},"12001200":{"10000002":250,"10000003":312,"10001016":296,"10001046":140},"12001300":{"10001022":200,"10001044":400,"10001053":200,"10001080":200},"12001301":{"10001008":145,"10001022":112,"10001053":145,"10001066":112,"10001068":225,"10001080":145,"10001086":112},"12001302":{},"12001303":{},"12001400":{"10001003":74,"10001010":180,"10001012":74,"10001018":74,"10001019":74,"10001040":148,"10001043":74,"10001067":74,"10001089":223},"12001500":{"10001013":333,"10001014":666},"12001501":{},"12001502":{},"12001503":{"10001032":500,"10001060":250,"10001068":250},"12001504":{"10001068":1000},"12001505":{"10001032":1000},"12001600":{"10000005":112,"10001029":516,"10001036":370},"12001700":{"10001021":132,"10001060":132,"10001071":75,"10001073":132,"10001093":132,"10001096":132,"10001101":132,"10001102":132},"12001701":{},"12001702":{},"12001703":{},"12001704":{},"12001705":{},"12001706":{"10001023":166,"10001061":500,"10001088":166,"10001126":166},"12001707":{},"12001708":{},"12001800":{"10001038":218,"10001078":281,"10001079":500},"12001801":{"10001015":92,"10001020":210,"10001025":210,"10001030":92,"10001057":92,"10001083":92,"10001084":210},"12001900":{"10001005":79,"10001047":79,"10001051":79,"10001075":79,"10001076":340,"10001085":79,"10001086":261},"12002000":{"10001004":93,"10001007":93,"10001037":93,"10001041":320,"10001048":186,"10001050":120,"10001098":93},"12010000":{},"12010100":{},"12010101":{},"12010102":{},"12010200":{},"12010300":{},"12010400":{},"12010500":{},"12010600":{},"12010700":{},"12010701":{},"12010800":{},"12010900":{"10001131":1000},"12011000":{},"12011100":{},"12011200":{},"12011300":{},"12011400":{},"12011401":{},"level":"info","message":"subscribe nation_share_rate_updated","timestamp":"2025-08-01T07:00:01.066Z"}
{"powers":{"10000000":5,"10000001":6,"10000002":5,"10000003":4,"10000005":6},"level":"info","message":"subscribe national_power_updated","timestamp":"2025-08-01T07:00:01.067Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-01T07:02:21.302Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-01T07:02:21.302Z"}
{"level":"info","message":"[Session] socket disposed, 2o83uJcI","timestamp":"2025-08-01T07:02:21.302Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-01T07:02:21.436Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:02:21.436Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:02:21.669Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-01T07:02:21.670Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-01T07:02:21.670Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:02:21.672Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-01T07:02:21.673Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:02:21.673Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:02:21.674Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:02:21.674Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:02:21.674Z"}
{"environment":"development","type":"oceand","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:02:26.267Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.396Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.396Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.397Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.397Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.397Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.397Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.397Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.398Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:02:55.398Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:02:55.410Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-01T07:02:55.413Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:02:54.079Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:02:54.080Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:02:54.081Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:02:54.082Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:02:58.146Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:02:58.147Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:02:58.147Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:02:58.154Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:02:58.155Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:02:58.173Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:02:58.257Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.278Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.294Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.308Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.323Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.340Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.360Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.378Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.395Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:02:58.413Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:02:58.498Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:02:58.499Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:02:58.503Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:02:58.643Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:02:58.645Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:02:58.646Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:02:58.646Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:02:58.649Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:02:58.649Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:02:58.650Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:02:58.650Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:02:58.652Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:02:58.652Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:02:58.653Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:02:58.653Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:02:58.654Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:02:58.656Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:02:58.657Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-01T07:02:58.764Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-01T07:02:58.808Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:58.813Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:58.814Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:58.814Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:58.814Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:02:58.876Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:02:58.889Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-01T07:02:58.892Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-01T07:02:58.893Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:02:58.895Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-01T07:02:58.896Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:02:58.898Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:02:58.898Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1754031778,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-01T07:02:58.992Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-01T07:02:58.992Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:02:59.905Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:02:59.905Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:03:00.909Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:03:00.909Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1754031781,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-01T07:03:01.011Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-01T07:03:01.012Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:03:01.915Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:03:01.915Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:03:02.995Z"}
{"pingInterval":2000,"curDate":1754031782,"level":"info","message":"registration succeeded.","timestamp":"2025-08-01T07:03:02.999Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-01T07:03:11.548Z"}
{"level":"info","message":"[SessionManager] session created: cP1-B6u3, for: 127.0.0.1, session count: 1","timestamp":"2025-08-01T07:03:11.840Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-01T07:03:11.841Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-01T07:03:11.841Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-01T07:03:11.842Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.844Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.846Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.846Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.846Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.847Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.847Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.847Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:03:11.847Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-01T07:12:30.395Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-01T07:12:30.395Z"}
{"level":"info","message":"[Session] socket disposed, cP1-B6u3","timestamp":"2025-08-01T07:12:30.396Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-01T07:12:30.563Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:12:30.563Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:12:30.847Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-01T07:12:30.847Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-01T07:12:30.848Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:12:30.850Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-01T07:12:30.851Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:12:30.851Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:12:30.851Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:12:30.852Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:12:30.852Z"}
{"environment":"development","type":"oceand","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:20:44.237Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.951Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.952Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.952Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.952Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.952Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.953Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.953Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.953Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:21:06.954Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":4},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:21:06.971Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-01T07:21:06.974Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:21:08.501Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:21:08.502Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:21:08.502Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:21:08.504Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:21:12.540Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:21:12.541Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:21:12.542Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:21:12.549Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:21:12.550Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:21:12.564Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:21:12.649Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.670Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.688Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.700Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.714Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.727Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.745Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.763Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.782Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:21:12.801Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:21:12.886Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:21:12.886Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:21:12.890Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:21:13.040Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:21:13.043Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:21:13.045Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:21:13.045Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:21:13.048Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:21:13.048Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:21:13.048Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:21:13.048Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:21:13.050Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:21:13.051Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:21:13.051Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:21:13.052Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:21:13.053Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:21:13.055Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:21:13.055Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-01T07:21:13.161Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-01T07:21:13.298Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:13.303Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:13.303Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:13.304Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:13.304Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:21:13.366Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:21:13.377Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-01T07:21:13.380Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-01T07:21:13.380Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":5},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:21:13.396Z"}
{"layoutVersion":5,"level":"verbose","message":"sync config to...","timestamp":"2025-08-01T07:21:13.398Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-01T07:21:13.398Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:21:13.408Z"}
{"pingInterval":2000,"curDate":1754032873,"level":"info","message":"registration succeeded.","timestamp":"2025-08-01T07:21:13.413Z"}
{"worldId":"UWO-GL-01","newlayoutVersion":7,"curLayoutVersion":5,"level":"info","message":"config-pubsub : new instance register msg received ...","timestamp":"2025-08-01T07:21:20.574Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:21:20.579Z"}
{"layoutVersion":7,"level":"verbose","message":"sync config to...","timestamp":"2025-08-01T07:21:20.580Z"}
{"level":"info","message":"received a new instance in layout[7] but nothing to do currently","timestamp":"2025-08-01T07:21:20.580Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":50000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-01T07:21:28.853Z"}
{"level":"info","message":"[SessionManager] session created: _K_4vsa3, for: 127.0.0.1, session count: 1","timestamp":"2025-08-01T07:21:29.931Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-01T07:21:29.933Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-01T07:21:29.934Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":2,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-01T07:21:29.935Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.937Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.939Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.940Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.940Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.940Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.940Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.940Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:21:29.941Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-01T07:50:40.302Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:50:40.302Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:50:41.232Z"}
{"level":"info","message":"[Session] socket disposed, _K_4vsa3","timestamp":"2025-08-01T07:50:41.233Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-01T07:50:41.233Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-01T07:50:41.234Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:50:41.235Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-01T07:50:41.236Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:50:41.236Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:50:41.237Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:50:41.237Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:50:41.237Z"}
{"environment":"development","type":"oceand","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:50:45.177Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.719Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.719Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.720Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.720Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.720Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.720Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.720Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.721Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:51:09.721Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:51:09.731Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-01T07:51:09.736Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:51:11.297Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:51:11.297Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:51:11.297Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:51:11.298Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:51:12.888Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:51:12.888Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:51:12.889Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:51:12.896Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:51:12.896Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:51:12.913Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:51:13.001Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.023Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.040Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.052Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.067Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.079Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.097Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.115Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.131Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:51:13.149Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:51:13.234Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:51:13.235Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:51:13.239Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:51:13.388Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:51:13.391Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:51:13.392Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:51:13.392Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:51:13.396Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:51:13.396Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:51:13.397Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:51:13.397Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:51:13.400Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:51:13.401Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:51:13.401Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:51:13.402Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:51:13.403Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:51:13.405Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:51:13.406Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-01T07:51:13.501Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-01T07:51:13.554Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:13.559Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:13.559Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:13.559Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:13.559Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:51:13.631Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:51:13.650Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-01T07:51:13.653Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-01T07:51:13.654Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:51:13.656Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-01T07:51:13.657Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:51:13.659Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:51:13.659Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1754034673,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-01T07:51:13.753Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-01T07:51:13.753Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:51:14.663Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:51:14.663Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:51:15.665Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:51:15.665Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1754034676,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-01T07:51:16.026Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-01T07:51:16.026Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:51:16.666Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:51:16.666Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:51:17.674Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:51:17.674Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:51:18.801Z"}
{"pingInterval":2000,"curDate":1754034678,"level":"info","message":"registration succeeded.","timestamp":"2025-08-01T07:51:18.809Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":50000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-01T07:51:30.207Z"}
{"level":"info","message":"[SessionManager] session created: OnspJEmD, for: 127.0.0.1, session count: 1","timestamp":"2025-08-01T07:51:30.508Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-01T07:51:30.510Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-01T07:51:30.510Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-01T07:51:30.511Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.512Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.514Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.514Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.514Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.515Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.515Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.515Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:51:30.515Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-01T07:55:01.326Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:55:01.326Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:55:02.008Z"}
{"level":"info","message":"[Session] socket disposed, OnspJEmD","timestamp":"2025-08-01T07:55:02.008Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-01T07:55:02.009Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-01T07:55:02.010Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:55:02.012Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-01T07:55:02.013Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:55:02.013Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:55:02.013Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:55:02.014Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:55:02.014Z"}
{"environment":"development","type":"oceand","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:55:05.802Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.988Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.988Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.989Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.989Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.989Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.989Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.990Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.990Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:55:30.990Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:55:31.000Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-01T07:55:31.008Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:55:32.640Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:55:32.641Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:55:32.641Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:55:32.642Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:55:36.636Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:55:36.637Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:55:36.638Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:55:36.645Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:55:36.646Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:55:36.663Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:55:36.749Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.774Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.791Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.803Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.819Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.832Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.852Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.872Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.893Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:55:36.915Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:55:36.999Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:55:37.000Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:55:37.004Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:55:37.147Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:55:37.149Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:55:37.150Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:55:37.151Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:55:37.153Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:55:37.154Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:55:37.154Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:55:37.154Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:55:37.156Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:55:37.156Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:55:37.157Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:55:37.157Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:55:37.158Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:55:37.160Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:55:37.161Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-01T07:55:37.258Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-01T07:55:37.318Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:37.324Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:37.324Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:37.324Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:37.324Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:55:37.403Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:55:37.419Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-01T07:55:37.422Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-01T07:55:37.422Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:55:37.424Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-01T07:55:37.425Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:55:37.427Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:55:37.427Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1754034937,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-01T07:55:37.522Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-01T07:55:37.522Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:55:35.472Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:55:35.472Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:55:36.477Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:55:36.477Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-01T07:55:37.478Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-01T07:55:37.478Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:55:38.540Z"}
{"pingInterval":2000,"curDate":1754034938,"level":"info","message":"registration succeeded.","timestamp":"2025-08-01T07:55:38.543Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":50000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":50000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":50000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-01T07:55:51.105Z"}
{"level":"info","message":"[SessionManager] session created: BF4eaKqN, for: 127.0.0.1, session count: 1","timestamp":"2025-08-01T07:55:51.369Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-01T07:55:51.375Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-01T07:55:51.375Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-01T07:55:51.376Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.378Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.380Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.380Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.380Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.380Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.381Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.381Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-01T07:55:51.381Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-01T07:59:10.574Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T07:59:10.575Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T07:59:11.297Z"}
{"level":"info","message":"[Session] socket disposed, BF4eaKqN","timestamp":"2025-08-01T07:59:11.297Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-01T07:59:11.298Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-01T07:59:11.298Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:59:11.300Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-01T07:59:11.301Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:59:11.301Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T07:59:11.302Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T07:59:11.302Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T07:59:11.303Z"}
{"environment":"development","type":"oceand","gitCommitHash":"bf713f9fa513","gitCommitMessage":"Merge branch 'cn_fgt' into cn_fgt_2","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-01T15:16:01+09:00","gitBranch":"cn_fgt_2","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-01T07:59:15.462Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.344Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.345Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.345Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.345Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.345Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.346Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.346Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.346Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-01T07:59:44.346Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://grayfcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:59:44.361Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-01T07:59:44.363Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-01T07:59:45.860Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-01T07:59:45.861Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://grayfcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-01T07:59:45.862Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-01T07:59:45.864Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-01T07:59:49.644Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-01T07:59:49.645Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-01T07:59:49.646Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-01T07:59:49.653Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-01T07:59:49.654Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-01T07:59:49.671Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-01T07:59:49.761Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.783Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.799Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.812Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.826Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.839Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.856Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.874Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.891Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-01T07:59:49.910Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-01T07:59:49.994Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-01T07:59:49.996Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-01T07:59:50.000Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-01T07:59:50.147Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-01T07:59:50.150Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-01T07:59:50.150Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-01T07:59:50.151Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-01T07:59:50.153Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-01T07:59:50.154Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-01T07:59:50.154Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-01T07:59:50.154Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-01T07:59:50.156Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-01T07:59:50.157Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-01T07:59:50.157Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-01T07:59:50.157Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-01T07:59:50.158Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-01T07:59:50.160Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-01T07:59:50.161Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-01T07:59:50.261Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-01T07:59:50.306Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:50.311Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:50.312Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:50.312Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:50.312Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-01T07:59:50.365Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-01T07:59:50.381Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-01T07:59:50.385Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-01T07:59:50.385Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-01T07:59:50.387Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-01T07:59:50.388Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-01T07:59:50.468Z"}
{"pingInterval":2000,"curDate":1754035190,"level":"info","message":"registration succeeded.","timestamp":"2025-08-01T07:59:50.472Z"}
