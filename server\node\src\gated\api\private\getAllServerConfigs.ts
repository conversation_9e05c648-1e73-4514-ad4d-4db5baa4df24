// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Request, Response } from 'express';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { ServerInfoService } from '../../service/serverInfoService';
import { GetAllServerConfigsResponse, ClientOS, ServerState } from '../../model/serverInfo';

const serverInfoService = new ServerInfoService();

export = async (req: Request, res: Response) => {
  const { page, pageSize, os, state, searchTerm } = req.query;

  mlog.info('[RX] GET /getAllServerConfigs', { 
    page, 
    pageSize, 
    os, 
    state, 
    searchTerm: typeof searchTerm === 'string' ? searchTerm.substring(0, 50) : searchTerm
  });

  try {
    // 쿼리 파라미터 파싱 및 검증
    const parsedPage = page ? parseInt(page as string, 10) : undefined;
    const parsedPageSize = pageSize ? parseInt(pageSize as string, 10) : undefined;

    if (parsedPage !== undefined && (parsedPage < 1 || !Number.isInteger(parsedPage))) {
      throw new MError('Invalid page number (must be positive integer)', MErrorCode.GATE_INVALID_REQUEST);
    }

    if (parsedPageSize !== undefined && (parsedPageSize < 1 || parsedPageSize > 100 || !Number.isInteger(parsedPageSize))) {
      throw new MError('Invalid page size (must be 1-100)', MErrorCode.GATE_INVALID_REQUEST);
    }

    if (os && !Object.values(ClientOS).includes(os as ClientOS)) {
      throw new MError('Invalid OS value', MErrorCode.GATE_INVALID_OS);
    }

    if (state && !Object.values(ServerState).includes(state as ServerState)) {
      throw new MError('Invalid state value', MErrorCode.GATE_INVALID_STATE);
    }

    if (searchTerm && typeof searchTerm === 'string' && searchTerm.length > 100) {
      throw new MError('Search term too long (max 100 characters)', MErrorCode.GATE_INVALID_REQUEST);
    }

    const result = await serverInfoService.getAllServerConfigs({
      page: parsedPage,
      pageSize: parsedPageSize,
      os: os as ClientOS,
      state: state as ServerState,
      searchTerm: searchTerm as string
    });

    mlog.info('[TX] GET /getAllServerConfigs', { 
      count: result.configs.length,
      totalCount: result.pagination.totalCount,
      currentPage: result.pagination.currentPage,
      totalPages: result.pagination.totalPages
    });

    res.json(result);
  } catch (error) {
    mlog.error('Error in GET getAllServerConfigs', error);
    
    if (error instanceof MError) {
      throw error;
    }
    
    throw new MError(error.message, MErrorCode.GATE_GET_ALL_SERVER_CONFIGS_ERROR, undefined, error.stack);
  }
};
