{"name": "uwo-server", "version": "1.0.0", "license": "MIT", "private": true, "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 100, "arrowParens": "always"}, "scripts": {"------# start #------": "", "start:configd": "node -r source-map-support/register dist/configd/configd.js", "start:authd": "node -r source-map-support/register dist/authd/authd.js", "start:alockd": "node -r source-map-support/register dist/alockd/alockd.js", "start:realmd": "node -r source-map-support/register dist/realmd/realmd.js", "start:lobbyd": "node -r source-map-support/register dist/lobbyd/lobbyd.js", "start:oceand": "node -r source-map-support/register dist/oceand/oceand.js", "start:saild": "node -r source-map-support/register dist/saild/saild.js", "start:townd": "node -r source-map-support/register dist/townd/townd.js", "start:zonelbd": "node -r source-map-support/register dist/zonelbd/zonelbd.js", "start:judged": "node -r source-map-support/register dist/judged/judged.js", "start:ffid": "node -r source-map-support/register dist/ffid/ffid.js", "start:gated": "node -r source-map-support/register dist/gated/index.js", "start:testd": "node -r source-map-support/register dist/testd/testd.js", "start:replayActionLogs": "node -r source-map-support/register dist/judged/replayActionLogs.js", "------# watch #------": "", "watch:configd": "nodemon -i log/* -r source-map-support/register dist/configd/configd.js", "watch:authd": "nodemon -i log/* -r source-map-support/register dist/authd/authd.js", "watch:alockd": "nodemon -i log/* -r source-map-support/register dist/alockd/alockd.js", "watch:realmd": "nodemon -i log/* -r source-map-support/register dist/realmd/realmd.js", "watch:lobbyd": "nodemon -i log/* -r source-map-support/register dist/lobbyd/lobbyd.js", "watch:oceand": "nodemon -i log/* -r source-map-support/register dist/oceand/oceand.js", "watch:saild": "nodemon -i log/* -r source-map-support/register dist/saild/saild.js", "watch:townd": "nodemon -i log/* -r source-map-support/register dist/townd/townd.js", "watch:zonelbd": "nodemon -i log/* -r source-map-support/register dist/zonelbd/zonelbd.js", "watch:gated": "nodemon -i log/* -r source-map-support/register dist/gated/index.js", "watch:judged": "nodemon -i log/* -r source-map-support/register dist/judged/judged.js", "------# production #------": "", "start:prod:configd": "NODE_ENV=production node -r source-map-support/register --prof dist/configd/configd.js", "start:prod:authd": "NODE_ENV=production node -r source-map-support/register --prof dist/authd/authd.js", "start:prod:alockd": "NODE_ENV=production node -r source-map-support/register --prof dist/alockd/alockd.js", "start:prod:realmd": "NODE_ENV=production node -r source-map-support/register --prof dist/realmd/realmd.js", "start:prod:lobbyd": "NODE_ENV=production node -r source-map-support/register --prof dist/lobbyd/lobbyd.js", "start:prod:oceand": "NODE_ENV=production node -r source-map-support/register --prof dist/oceand/oceand.js", "start:prod:saild": "NODE_ENV=production node -r source-map-support/register --prof dist/saild/saild.js", "start:prod:townd": "NODE_ENV=production node -r source-map-support/register --prof dist/townd/townd.js", "start:prod:zonelbd": "NODE_ENV=production node -r source-map-support/register --prof dist/zonelbd/zonelbd.js", "start:prod:judged": "NODE_ENV=production node -r source-map-support/register --prof dist/judged/judged.js", "start:prod:ffid": "NODE_ENV=production node -r source-map-support/register --prof dist/ffid/ffid.js", "start:prod:gated": "NODE_ENV=production node -r source-map-support/register --prof dist/gated/index.js", "------# deploy #------": "", "deploy:lightsail:init": "node -r source-map-support/register dist/deploy/initLightsail.js", "deploy:lightsail:update": "node -r source-map-support/register dist/deploy/updateLightsail.js", "deploy:lgsqldiff": "node -r source-map-support/register dist/deploy/lgSqlDiff.js", "deploy:FileBeat:init": "node -r source-map-support/register dist/deploy/initFilebeat.js", "------# judge db init #------": "", "judge:initMongoDb": "node -r source-map-support/register dist/judged/initMongoDb.js", "------# deploy databse #------": "", "mig": "node -r source-map-support/register dist/deploy/migrateRdb.js", "mig-world": "node -r source-map-support/register dist/deploy/migrateRdb.js --world 1", "------# databse #------": "", "db-create": "node -r source-map-support/register dist/migrate/createDatabase.js", "db-drop": "node -r source-map-support/register dist/migrate/dropDatabase.js", "mig-create": "node -r source-map-support/register dist/migrate/createMigration.js", "mig-up": "node -r source-map-support/register dist/deploy/migrateRdb.js --env user --target table", "mig-down": "node -r source-map-support/register dist/deploy/migrateRdb.js --env user --target table --direction down", "sp-apply": "node -r source-map-support/register dist/deploy/migrateRdb.js --env user --target procedure", "------# testing #------": "", "start:botClientApp": "node -r source-map-support/register dist/botClientApp/botClientApp.js", "start:dbPopulaterApp": "node  -r source-map-support/register dist/dbPopulaterApp/dbPopulaterApp.js ", "------# common #------": "", "build": "tsc -p tsconfig.json", "clean": "<PERSON><PERSON><PERSON> dist", "watch": "tsc -w", "reconfigure": "node -r source-map-support/register dist/configd/reconfigure.js", "pm2": "pm2 start ../pm2/dev.ecosystem.config.js", "------# devonly #------": "", "devonly:reset-local-redis": "node -r source-map-support/register dist/devOnly/resetLocalRedis.js", "devonly:switch-binarycode": "../scripts/switch_binarycode.sh", "devonly:check-binarycode": "../scripts/check_binarycode.sh"}, "dependencies": {"@sentry/node": "^5.24.2", "ajv": "^6.10.0", "amqplib": "^0.5.2", "async": "^3.1.0", "axios": "^0.19.2", "behaviortree": "^2.0.3", "bluebird": "^3.7.1", "config": "^3.2.4", "csv-parser": "^3.2.0", "db-migrate": "^0.11.3", "db-migrate-mysql": "^1.1.10", "event-dispatch": "^0.4.1", "express": "^4.16.2", "express-async-errors": "^3.1.1", "express-ipfilter": "^1.1.2", "ffi-napi": "^4.0.3", "fifo": "^2.3.0", "generic-pool": "^3.2.0", "gl-matrix": "^3.0.0", "graphlib": "^2.1.5", "handlebars": "^4.7.8", "ioredis": "^4.14.1", "js-yaml": "^4.1.0", "json5": "^2.1.1", "knex": "^0.20.1", "lodash": "^4.17.11", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "moment": "^2.19.1", "mongoose": "^5.11.13", "morgan": "^1.9.0", "nanoid": "3.3.4", "node-schedule": "^1.3.0", "object-hash": "^2.0.0", "pick-deep": "^1.0.0", "promise-mysql": "^4.1.1", "qs": "^6.9.1", "queue-fifo": "^0.2.6", "ramda": "^0.26.1", "reflect-metadata": "^0.1.13", "rxjs": "^6.2.2", "sf": "^0.2.0", "smart-buffer": "^4.1.0", "source-map-support": "^0.5.4", "stoppable": "^1.1.0", "typedi": "^0.8.0", "url-join": "^4.0.1", "winston": "^3.2.1", "winston-daily-rotate-file": "^4.1.0", "zlib": "^1.0.5"}, "devDependencies": {"@types/amqplib": "^0.5.13", "@types/async": "^3.0.3", "@types/bluebird": "^3.5.36", "@types/config": "^0.0.36", "@types/express": "^4.17.2", "@types/ffi-napi": "^4.0.5", "@types/generic-pool": "^3.1.9", "@types/gl-matrix": "^2.4.5", "@types/graphlib": "^2.1.5", "@types/ioredis": "^4.0.19", "@types/json5": "^0.0.30", "@types/lodash": "^4.14.147", "@types/minimist": "^1.2.0", "@types/mkdirp": "^0.5.2", "@types/morgan": "^1.7.37", "@types/nanoid": "^3.0.0", "@types/node": "^12.12.7", "@types/node-schedule": "^1.2.4", "@types/object-hash": "^1.3.0", "@types/qs": "^6.9.0", "@types/ramda": "^0.26.34", "@types/source-map-support": "^0.5.10", "@types/triple-beam": "^1.3.0", "@types/url-join": "^4.0.0", "nodemon": "^1.19.4", "rewire": "^4.0.1", "rimraf": "^3.0.0", "tslint-config-prettier": "^1.18.0", "typescript": "^4.5.5"}}