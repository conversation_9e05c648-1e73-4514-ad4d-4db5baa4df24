{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-01T10:02:42.603Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-01T10:02:42.605Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-01T10:02:43.462Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:43.465Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-01T10:02:43.466Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-01T10:02:43.467Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-01T10:02:43.468Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:43.468Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:43.468Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-01T10:02:43.468Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:43.469Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:43.469Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-01T10:02:43.469Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:43.469Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:43.470Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-01T10:02:43.470Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-01T10:02:43.470Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-01T10:02:43.471Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-01T10:02:43.471Z"}
