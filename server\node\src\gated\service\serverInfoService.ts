// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import { DBConnPool } from '../../mysqllib/pool';
import { MError, MErrorCode } from '../../motiflib/merror';
import mlog from '../../motiflib/mlog';
import {
  ServerConfig,
  ClientOS,
  ServerState,
  GetServerInfoRequest,
  GetServerInfoResponse,
  UpdateServerStateRequest,
  SetMaintenanceMessageRequest,
  UpdateServerConfigRequest
} from '../model/serverInfo';

export class ServerInfoService {
  private dbConnPool: DBConnPool;

  constructor() {
    this.dbConnPool = Container.get(DBConnPool);
  }

  async getServerInfo(request: GetServerInfoRequest): Promise<GetServerInfoResponse> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        SELECT state, gameServerAddress, gameServerPort, patchServerAddress, 
               guestModeAllowed, maintenanceMessage, externalUrl, customValue
        FROM g_server_configs 
        WHERE os = ? AND version = ?
      `;
      
      const results = await pool.query(query, [request.os, request.version]);
      
      if (!results || results.length === 0) {
        // 해당 OS/버전에 대한 설정이 없으면 기본값 반환
        mlog.warn('No server config found for OS/version', { os: request.os, version: request.version });
        return {
          state: ServerState.OFFLINE,
          gameServerAddress: '',
          gameServerPort: 8080,
          patchServerAddress: '',
          guestModeAllowed: false,
          maintenanceMessage: 'Server configuration not found'
        };
      }

      const config = results[0];
      return {
        state: config.state as ServerState,
        gameServerAddress: config.gameServerAddress,
        gameServerPort: config.gameServerPort,
        patchServerAddress: config.patchServerAddress,
        guestModeAllowed: !!config.guestModeAllowed,
        maintenanceMessage: config.maintenanceMessage,
        externalUrl: config.externalUrl,
        customValue: config.customValue
      };
    } catch (error) {
      mlog.error('Failed to get server info', error);
      throw new MError('Failed to get server info', MErrorCode.GATE_GET_SERVER_INFO_ERROR);
    }
  }

  async updateServerState(request: UpdateServerStateRequest): Promise<void> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        UPDATE g_server_configs 
        SET state = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE os = ? AND version = ?
      `;
      
      const result = await pool.query(query, [request.state, request.os, request.version]);
      
      if (result.affectedRows === 0) {
        throw new MError('Server config not found', MErrorCode.GATE_SERVER_CONFIG_NOT_FOUND);
      }

      mlog.info('Server state updated', { os: request.os, version: request.version, state: request.state });
    } catch (error) {
      mlog.error('Failed to update server state', error);
      throw error instanceof MError ? error : new MError('Failed to update server state', MErrorCode.GATE_UPDATE_SERVER_STATE_ERROR);
    }
  }

  async setMaintenanceMessage(request: SetMaintenanceMessageRequest): Promise<void> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        UPDATE g_server_configs 
        SET maintenanceMessage = ?, externalUrl = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE os = ? AND version = ?
      `;
      
      const result = await pool.query(query, [
        request.maintenanceMessage, 
        request.externalUrl || null, 
        request.os, 
        request.version
      ]);
      
      if (result.affectedRows === 0) {
        throw new MError('Server config not found', MErrorCode.GATE_SERVER_CONFIG_NOT_FOUND);
      }

      mlog.info('Maintenance message updated', { 
        os: request.os, 
        version: request.version, 
        message: request.maintenanceMessage 
      });
    } catch (error) {
      mlog.error('Failed to set maintenance message', error);
      throw error instanceof MError ? error : new MError('Failed to set maintenance message', MErrorCode.GATE_SET_MAINTENANCE_MESSAGE_ERROR);
    }
  }

  async updateServerConfig(request: UpdateServerConfigRequest): Promise<void> {
    try {
      const pool = this.dbConnPool.getPool();
      
      // 동적으로 업데이트할 필드들을 구성
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      
      if (request.gameServerAddress !== undefined) {
        updateFields.push('gameServerAddress = ?');
        updateValues.push(request.gameServerAddress);
      }
      
      if (request.gameServerPort !== undefined) {
        updateFields.push('gameServerPort = ?');
        updateValues.push(request.gameServerPort);
      }
      
      if (request.patchServerAddress !== undefined) {
        updateFields.push('patchServerAddress = ?');
        updateValues.push(request.patchServerAddress);
      }
      
      if (request.guestModeAllowed !== undefined) {
        updateFields.push('guestModeAllowed = ?');
        updateValues.push(request.guestModeAllowed ? 1 : 0);
      }
      
      if (request.customValue !== undefined) {
        updateFields.push('customValue = ?');
        updateValues.push(request.customValue);
      }
      
      if (request.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(request.description);
      }
      
      if (updateFields.length === 0) {
        throw new MError('No fields to update', MErrorCode.GATE_NO_FIELDS_TO_UPDATE);
      }
      
      updateFields.push('updatedAt = CURRENT_TIMESTAMP');
      updateValues.push(request.os, request.version);
      
      const query = `
        UPDATE g_server_configs 
        SET ${updateFields.join(', ')}
        WHERE os = ? AND version = ?
      `;
      
      const result = await pool.query(query, updateValues);
      
      if (result.affectedRows === 0) {
        throw new MError('Server config not found', MErrorCode.GATE_SERVER_CONFIG_NOT_FOUND);
      }

      mlog.info('Server config updated', { os: request.os, version: request.version, fields: updateFields });
    } catch (error) {
      mlog.error('Failed to update server config', error);
      throw error instanceof MError ? error : new MError('Failed to update server config', MErrorCode.GATE_UPDATE_SERVER_CONFIG_ERROR);
    }
  }

  async getAllServerConfigs(): Promise<ServerConfig[]> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        SELECT id, os, version, state, gameServerAddress, gameServerPort, 
               patchServerAddress, guestModeAllowed, maintenanceMessage, 
               externalUrl, customValue, description, createdAt, updatedAt
        FROM g_server_configs 
        ORDER BY os, version
      `;
      
      const results = await pool.query(query);
      return results.map((row: any) => ({
        id: row.id,
        os: row.os as ClientOS,
        version: row.version,
        state: row.state as ServerState,
        gameServerAddress: row.gameServerAddress,
        gameServerPort: row.gameServerPort,
        patchServerAddress: row.patchServerAddress,
        guestModeAllowed: !!row.guestModeAllowed,
        maintenanceMessage: row.maintenanceMessage,
        externalUrl: row.externalUrl,
        customValue: row.customValue,
        description: row.description,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt
      }));
    } catch (error) {
      mlog.error('Failed to get all server configs', error);
      throw new MError('Failed to get all server configs', MErrorCode.GATE_GET_ALL_SERVER_CONFIGS_ERROR);
    }
  }
}
