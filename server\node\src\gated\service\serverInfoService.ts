// ----------------------------------------------------------------------------
// COPYRIGHT (C)2025 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import { DBConnPool } from '../../mysqllib/pool';
import { MError, MErrorCode } from '../../motiflib/merror';
import mlog from '../../motiflib/mlog';
import {
  ServerConfig,
  ClientOS,
  ServerState,
  GetServerInfoRequest,
  GetServerInfoResponse,
  UpdateServerStateRequest,
  SetMaintenanceMessageRequest,
  UpdateServerConfigRequest,
  GetAllServerConfigsRequest,
  GetAllServerConfigsResponse,
  CreateServerConfigRequest,
  CreateServerConfigResponse
} from '../model/serverInfo';

export class ServerInfoService {
  private dbConnPool: DBConnPool;

  constructor() {
    this.dbConnPool = Container.get(DBConnPool);
  }

  async getServerInfo(request: GetServerInfoRequest): Promise<GetServerInfoResponse> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        SELECT state, gameServerAddress, gameServerPort, patchServerAddress, 
               guestModeAllowed, maintenanceMessage, externalUrl, customValue
        FROM g_server_configs 
        WHERE os = ? AND version = ?
      `;
      
      const results = await pool.query(query, [request.os, request.version]);
      
      if (!results || results.length === 0) {
        // 해당 OS/버전에 대한 설정이 없으면 기본값 반환
        mlog.warn('No server config found for OS/version', { os: request.os, version: request.version });
        return {
          state: ServerState.OFFLINE,
          gameServerAddress: '',
          gameServerPort: 8080,
          patchServerAddress: '',
          guestModeAllowed: false,
          maintenanceMessage: 'Server configuration not found'
        };
      }

      const config = results[0];
      return {
        state: config.state as ServerState,
        gameServerAddress: config.gameServerAddress,
        gameServerPort: config.gameServerPort,
        patchServerAddress: config.patchServerAddress,
        guestModeAllowed: !!config.guestModeAllowed,
        maintenanceMessage: config.maintenanceMessage,
        externalUrl: config.externalUrl,
        customValue: config.customValue
      };
    } catch (error) {
      mlog.error('Failed to get server info', error);
      throw new MError('Failed to get server info', MErrorCode.GATE_GET_SERVER_INFO_ERROR);
    }
  }

  async updateServerState(request: UpdateServerStateRequest): Promise<void> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        UPDATE g_server_configs 
        SET state = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE os = ? AND version = ?
      `;
      
      const result = await pool.query(query, [request.state, request.os, request.version]);
      
      if (result.affectedRows === 0) {
        throw new MError('Server config not found', MErrorCode.GATE_SERVER_CONFIG_NOT_FOUND);
      }

      mlog.info('Server state updated', { os: request.os, version: request.version, state: request.state });
    } catch (error) {
      mlog.error('Failed to update server state', error);
      throw error instanceof MError ? error : new MError('Failed to update server state', MErrorCode.GATE_UPDATE_SERVER_STATE_ERROR);
    }
  }

  async setMaintenanceMessage(request: SetMaintenanceMessageRequest): Promise<void> {
    try {
      const pool = this.dbConnPool.getPool();
      const query = `
        UPDATE g_server_configs 
        SET maintenanceMessage = ?, externalUrl = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE os = ? AND version = ?
      `;
      
      const result = await pool.query(query, [
        request.maintenanceMessage, 
        request.externalUrl || null, 
        request.os, 
        request.version
      ]);
      
      if (result.affectedRows === 0) {
        throw new MError('Server config not found', MErrorCode.GATE_SERVER_CONFIG_NOT_FOUND);
      }

      mlog.info('Maintenance message updated', { 
        os: request.os, 
        version: request.version, 
        message: request.maintenanceMessage 
      });
    } catch (error) {
      mlog.error('Failed to set maintenance message', error);
      throw error instanceof MError ? error : new MError('Failed to set maintenance message', MErrorCode.GATE_SET_MAINTENANCE_MESSAGE_ERROR);
    }
  }

  async updateServerConfig(request: UpdateServerConfigRequest): Promise<void> {
    try {
      const pool = this.dbConnPool.getPool();
      
      // 동적으로 업데이트할 필드들을 구성
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      
      if (request.gameServerAddress !== undefined) {
        updateFields.push('gameServerAddress = ?');
        updateValues.push(request.gameServerAddress);
      }
      
      if (request.gameServerPort !== undefined) {
        updateFields.push('gameServerPort = ?');
        updateValues.push(request.gameServerPort);
      }
      
      if (request.patchServerAddress !== undefined) {
        updateFields.push('patchServerAddress = ?');
        updateValues.push(request.patchServerAddress);
      }
      
      if (request.guestModeAllowed !== undefined) {
        updateFields.push('guestModeAllowed = ?');
        updateValues.push(request.guestModeAllowed ? 1 : 0);
      }
      
      if (request.customValue !== undefined) {
        updateFields.push('customValue = ?');
        updateValues.push(request.customValue);
      }
      
      if (request.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(request.description);
      }
      
      if (updateFields.length === 0) {
        throw new MError('No fields to update', MErrorCode.GATE_NO_FIELDS_TO_UPDATE);
      }
      
      updateFields.push('updatedAt = CURRENT_TIMESTAMP');
      updateValues.push(request.os, request.version);
      
      const query = `
        UPDATE g_server_configs 
        SET ${updateFields.join(', ')}
        WHERE os = ? AND version = ?
      `;
      
      const result = await pool.query(query, updateValues);
      
      if (result.affectedRows === 0) {
        throw new MError('Server config not found', MErrorCode.GATE_SERVER_CONFIG_NOT_FOUND);
      }

      mlog.info('Server config updated', { os: request.os, version: request.version, fields: updateFields });
    } catch (error) {
      mlog.error('Failed to update server config', error);
      throw error instanceof MError ? error : new MError('Failed to update server config', MErrorCode.GATE_UPDATE_SERVER_CONFIG_ERROR);
    }
  }

  async getAllServerConfigs(request: GetAllServerConfigsRequest = {}): Promise<GetAllServerConfigsResponse> {
    try {
      const pool = this.dbConnPool.getPool();

      // 기본값 설정
      const page = request.page || 1;
      const pageSize = Math.min(request.pageSize || 20, 100); // 최대 100개로 제한
      const offset = (page - 1) * pageSize;

      // WHERE 조건 구성
      const whereConditions: string[] = [];
      const queryParams: any[] = [];

      if (request.os) {
        whereConditions.push('os = ?');
        queryParams.push(request.os);
      }

      if (request.state) {
        whereConditions.push('state = ?');
        queryParams.push(request.state);
      }

      if (request.searchTerm) {
        whereConditions.push('(version LIKE ? OR description LIKE ? OR gameServerAddress LIKE ?)');
        const searchPattern = `%${request.searchTerm}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 총 개수 조회
      const countQuery = `
        SELECT COUNT(*) as totalCount
        FROM g_server_configs
        ${whereClause}
      `;

      const countResult = await pool.query(countQuery, queryParams);
      const totalCount = countResult[0].totalCount;
      const totalPages = Math.ceil(totalCount / pageSize);

      // 데이터 조회
      const dataQuery = `
        SELECT id, os, version, state, gameServerAddress, gameServerPort,
               patchServerAddress, guestModeAllowed, maintenanceMessage,
               externalUrl, customValue, description, createdAt, updatedAt
        FROM g_server_configs
        ${whereClause}
        ORDER BY os, version
        LIMIT ? OFFSET ?
      `;

      const dataParams = [...queryParams, pageSize, offset];
      const results = await pool.query(dataQuery, dataParams);

      const configs = results.map((row: any) => ({
        id: row.id,
        os: row.os as ClientOS,
        version: row.version,
        state: row.state as ServerState,
        gameServerAddress: row.gameServerAddress,
        gameServerPort: row.gameServerPort,
        patchServerAddress: row.patchServerAddress,
        guestModeAllowed: !!row.guestModeAllowed,
        maintenanceMessage: row.maintenanceMessage,
        externalUrl: row.externalUrl,
        customValue: row.customValue,
        description: row.description,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt
      }));

      return {
        configs,
        pagination: {
          currentPage: page,
          pageSize,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrevious: page > 1
        }
      };
    } catch (error) {
      mlog.error('Failed to get all server configs', error);
      throw new MError('Failed to get all server configs', MErrorCode.GATE_GET_ALL_SERVER_CONFIGS_ERROR);
    }
  }

  async createServerConfig(request: CreateServerConfigRequest): Promise<CreateServerConfigResponse> {
    try {
      const pool = this.dbConnPool.getPool();

      // 중복 확인
      const checkQuery = `
        SELECT id FROM g_server_configs
        WHERE os = ? AND version = ?
      `;

      const existingConfig = await pool.query(checkQuery, [request.os, request.version]);

      if (existingConfig.length > 0) {
        throw new MError('Server config already exists for this OS and version', MErrorCode.GATE_SERVER_CONFIG_ALREADY_EXISTS);
      }

      // 새 설정 생성
      const insertQuery = `
        INSERT INTO g_server_configs (
          os, version, state, gameServerAddress, gameServerPort,
          patchServerAddress, guestModeAllowed, maintenanceMessage,
          externalUrl, customValue, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const insertParams = [
        request.os,
        request.version,
        request.state || ServerState.OFFLINE,
        request.gameServerAddress,
        request.gameServerPort || 8080,
        request.patchServerAddress,
        request.guestModeAllowed ? 1 : 0,
        request.maintenanceMessage || null,
        request.externalUrl || null,
        request.customValue || null,
        request.description || null
      ];

      const result = await pool.query(insertQuery, insertParams);

      mlog.info('Server config created', {
        id: result.insertId,
        os: request.os,
        version: request.version
      });

      return {
        id: result.insertId,
        message: 'Server config created successfully'
      };
    } catch (error) {
      mlog.error('Failed to create server config', error);
      throw error instanceof MError ? error : new MError('Failed to create server config', MErrorCode.GATE_CREATE_SERVER_CONFIG_ERROR);
    }
  }
}
